#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GameLoop ADB Show Tabs Pro - Ultimate Professional Edition
أداة احترافية فاخرة للتحكم في GameLoop عبر ADB
تصميم أسود وذهبي عصري مع دقة عالية - tkinter version
"""

import os
import subprocess
import threading
import urllib.request
import zipfile
import time
import tkinter as tk
from tkinter import ttk, messagebox

class GameLoopADBPro:
    def __init__(self):
        self.root = tk.Tk()
        self.adb_folder = "platform-tools"
        self.adb_path = os.path.join(self.adb_folder, "adb.exe")
        self.current_device = None

        # إعدادات ADB المحسنة - 10 طرق لإخفاء النقاط
        self.adb_commands = {
            'enable_show_taps': 'settings put system show_touches 1',
            'disable_show_taps': 'settings put system show_touches 0',
            'get_show_taps_status': 'settings get system show_touches',

            # طرق إخفاء النقاط البيضاء - 10 طرق مختلفة
            'hide_method_1': 'settings put system show_touches 1 && settings put system show_touches 0 && settings put system show_touches 1 && settings put system show_touches 0',
            'hide_method_2': 'settings put system show_touches 1 && settings put secure show_touches 0',
            'hide_method_3': 'settings put system show_touches 1 && settings put global show_touches 0',
            'hide_method_4': 'settings put system show_touches 1 && setprop debug.show_touches false',
            'hide_method_5': 'settings put system show_touches 1 && settings put system show_touches 0 && sleep 0.1 && settings put system show_touches 1',
            'hide_method_6': 'settings put system show_touches 1 && settings delete system show_touches && settings put system show_touches 1',
            'hide_method_7': 'settings put system show_touches 1 && settings put system show_touches null',
            'hide_method_8': 'settings put system show_touches 1 && settings put system show_touches_alpha 0',
            'hide_method_9': 'settings put system show_touches 1 && settings put system touch_size_scale 0.01',
            'hide_method_10': 'settings put system show_touches 1 && settings put system pointer_size 0.1',

            # طريقة خاصة بـ GameLoop
            'gameloop_ultimate_hide': 'settings put system show_touches 1 && settings put system show_touches 0 && settings put system show_touches 1 && settings put global show_touches 0 && settings put secure show_touches 0 && settings put system show_touches_alpha 0',

            # إعادة النقاط
            'restore_touches': 'settings put system show_touches 1 && settings delete global show_touches && settings delete secure show_touches && settings put system show_touches_alpha 255',
        }

        self.setup_window()
        self.create_widgets()
        self.apply_luxury_style()
        self.check_adb_status()

    def setup_window(self):
        """إعداد النافذة الفاخرة"""
        self.root.title("🎮 GameLoop ADB Show Tabs Pro - Ultimate Edition")
        self.root.geometry("900x800")
        self.root.resizable(True, True)
        self.root.minsize(850, 750)
        self.root.configure(bg='#0a0a0a')

        # وضع النافذة في المنتصف
        self.center_window()

        # إضافة شفافية (Windows فقط)
        try:
            self.root.attributes('-alpha', 0.98)
        except:
            pass

    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (900 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"900x800+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة الفاخرة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#0a0a0a')
        main_frame.pack(fill='both', expand=True, padx=30, pady=30)

        # العنوان الفاخر
        self.create_header(main_frame)

        # قسم حالة ADB
        self.create_adb_section(main_frame)

        # قسم الاتصال
        self.create_connection_section(main_frame)

        # قسم التحكم في Show Tabs
        self.create_show_tabs_section(main_frame)

        # قسم الخيارات المتقدمة
        self.create_advanced_options(main_frame)

        # شريط الحالة الفاخر
        self.create_status_bar(main_frame)

    def create_header(self, parent):
        """إنشاء العنوان الفاخر"""
        header_frame = tk.Frame(parent, bg='#1a1a1a', relief='raised', bd=2)
        header_frame.pack(fill='x', pady=(0, 20))

        # العنوان الرئيسي
        title = tk.Label(header_frame,
                        text="🎮 GameLoop ADB Show Tabs Pro",
                        font=('Segoe UI Black', 24, 'bold'),
                        bg='#1a1a1a',
                        fg='#FFD700')
        title.pack(pady=(15, 5))

        # العنوان الفرعي
        subtitle = tk.Label(header_frame,
                           text="✨ Ultimate Professional Edition ✨",
                           font=('Segoe UI', 14, 'bold'),
                           bg='#1a1a1a',
                           fg='#FFA500')
        subtitle.pack(pady=(0, 5))

        # الوصف
        description = tk.Label(header_frame,
                              text="🚀 تحكم كامل في GameLoop • 🔘 إخفاء النقاط البيضاء بـ 10 طرق\n⚡ أداء فائق • 🎯 تصميم فاخر أسود وذهبي",
                              font=('Segoe UI', 10),
                              bg='#1a1a1a',
                              fg='#CCCCCC',
                              justify='center')
        description.pack(pady=(0, 15))

    def create_adb_section(self, parent):
        """قسم حالة ADB"""
        adb_frame = tk.LabelFrame(parent,
                                 text="🔧 حالة ADB",
                                 font=('Segoe UI Black', 12, 'bold'),
                                 bg='#1a1a1a',
                                 fg='#FFD700',
                                 bd=2,
                                 relief='groove')
        adb_frame.pack(fill='x', pady=(0, 15))

        self.adb_status = tk.Label(adb_frame,
                                  text="🔍 جاري فحص ADB...",
                                  font=('Segoe UI', 12, 'bold'),
                                  bg='#1a1a1a',
                                  fg='#FFA500')
        self.adb_status.pack(pady=15)

    def create_connection_section(self, parent):
        """قسم الاتصال"""
        conn_frame = tk.LabelFrame(parent,
                                  text="📱 الاتصال بـ GameLoop",
                                  font=('Segoe UI Black', 12, 'bold'),
                                  bg='#1a1a1a',
                                  fg='#FFD700',
                                  bd=2,
                                  relief='groove')
        conn_frame.pack(fill='x', pady=(0, 15))

        # حالة الاتصال
        self.connection_status = tk.Label(conn_frame,
                                         text="📱 غير متصل",
                                         font=('Segoe UI', 12, 'bold'),
                                         bg='#1a1a1a',
                                         fg='#FF4500')
        self.connection_status.pack(pady=(10, 5))

        # معلومات الجهاز
        self.device_info = tk.Label(conn_frame,
                                   text="",
                                   font=('Segoe UI', 10),
                                   bg='#1a1a1a',
                                   fg='#CCCCCC')
        self.device_info.pack(pady=(0, 10))

        # أزرار الاتصال
        buttons_frame = tk.Frame(conn_frame, bg='#1a1a1a')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 15))

        self.connect_btn = tk.Button(buttons_frame,
                                    text="🔌 اتصال بـ GameLoop",
                                    font=('Segoe UI Black', 11, 'bold'),
                                    bg='#FFD700',
                                    fg='#000000',
                                    activebackground='#FFFF00',
                                    activeforeground='#000000',
                                    relief='raised',
                                    bd=3,
                                    padx=20,
                                    pady=10,
                                    command=self.connect_to_gameloop)
        self.connect_btn.pack(side='left', padx=(0, 10), fill='x', expand=True)

        self.refresh_btn = tk.Button(buttons_frame,
                                    text="🔄 تحديث",
                                    font=('Segoe UI', 11, 'bold'),
                                    bg='#4a4a4a',
                                    fg='#FFD700',
                                    activebackground='#6a6a6a',
                                    activeforeground='#FFD700',
                                    relief='raised',
                                    bd=3,
                                    padx=20,
                                    pady=10,
                                    command=self.connect_to_gameloop)
        self.refresh_btn.pack(side='left', fill='x', expand=True)

    def create_show_tabs_section(self, parent):
        """قسم التحكم في Show Tabs"""
        tabs_frame = tk.LabelFrame(parent,
                                  text="🎯 التحكم في Show Tabs",
                                  font=('Segoe UI Black', 12, 'bold'),
                                  bg='#1a1a1a',
                                  fg='#FFD700',
                                  bd=2,
                                  relief='groove')
        tabs_frame.pack(fill='x', pady=(0, 15))

        # حالة Show Tabs
        self.show_tabs_status = tk.Label(tabs_frame,
                                        text="📋 Show Tabs: غير معروف",
                                        font=('Segoe UI', 12, 'bold'),
                                        bg='#1a1a1a',
                                        fg='#FFA500')
        self.show_tabs_status.pack(pady=(10, 5))

        # أزرار التحكم
        tabs_buttons_frame = tk.Frame(tabs_frame, bg='#1a1a1a')
        tabs_buttons_frame.pack(fill='x', padx=20, pady=(0, 15))

        self.enable_tabs_btn = tk.Button(tabs_buttons_frame,
                                        text="✅ تفعيل Show Tabs",
                                        font=('Segoe UI Black', 11, 'bold'),
                                        bg='#32CD32',
                                        fg='#000000',
                                        activebackground='#90EE90',
                                        activeforeground='#000000',
                                        relief='raised',
                                        bd=3,
                                        padx=20,
                                        pady=10,
                                        state='disabled',
                                        command=self.enable_show_tabs)
        self.enable_tabs_btn.pack(side='left', padx=(0, 10), fill='x', expand=True)

        self.disable_tabs_btn = tk.Button(tabs_buttons_frame,
                                         text="❌ إلغاء Show Tabs",
                                         font=('Segoe UI Black', 11, 'bold'),
                                         bg='#FF4500',
                                         fg='#FFFFFF',
                                         activebackground='#FF6347',
                                         activeforeground='#FFFFFF',
                                         relief='raised',
                                         bd=3,
                                         padx=20,
                                         pady=10,
                                         state='disabled',
                                         command=self.disable_show_tabs)
        self.disable_tabs_btn.pack(side='left', fill='x', expand=True)

    def create_advanced_options(self, parent):
        """الخيارات المتقدمة الفاخرة"""
        options_frame = tk.LabelFrame(parent,
                                     text="⚙️ خيارات متقدمة فاخرة",
                                     font=('Segoe UI Black', 12, 'bold'),
                                     bg='#1a1a1a',
                                     fg='#FFD700',
                                     bd=2,
                                     relief='groove')
        options_frame.pack(fill='x', pady=(0, 15))

        # خيار إخفاء النقاط
        self.hide_dots_var = tk.BooleanVar()
        self.hide_dots_check = tk.Checkbutton(options_frame,
                                             text="🔘 إخفاء النقاط البيضاء نهائياً (10 طرق احترافية)",
                                             variable=self.hide_dots_var,
                                             font=('Segoe UI Black', 12, 'bold'),
                                             bg='#1a1a1a',
                                             fg='#FFD700',
                                             selectcolor='#FFD700',
                                             activebackground='#1a1a1a',
                                             activeforeground='#FFFF00',
                                             command=self.toggle_hide_dots)
        self.hide_dots_check.pack(anchor='w', padx=20, pady=15)

    def create_status_bar(self, parent):
        """شريط الحالة الفاخر"""
        self.status_bar = tk.Label(parent,
                                  text="🚀 جاهز للاستخدام - أداة احترافية فاخرة",
                                  font=('Segoe UI', 10, 'bold'),
                                  bg='#2a2a2a',
                                  fg='#FFD700',
                                  relief='sunken',
                                  bd=2,
                                  anchor='w')
        self.status_bar.pack(fill='x', side='bottom', pady=(10, 0))

    def apply_luxury_style(self):
        """تطبيق التصميم الفاخر"""
        # تحسين مظهر الأزرار عند التمرير
        def on_enter(event):
            event.widget.config(relief='raised', bd=4)

        def on_leave(event):
            event.widget.config(relief='raised', bd=3)

        # ربط الأحداث بالأزرار
        for widget in [self.connect_btn, self.refresh_btn, self.enable_tabs_btn, self.disable_tabs_btn]:
            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=message)
        self.root.update()

    def run_adb_command(self, command):
        """تشغيل أمر ADB"""
        try:
            full_command = f'"{self.adb_path}" {command}'
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=10)
            return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
        except subprocess.TimeoutExpired:
            return False, "", "انتهت مهلة الأمر"
        except Exception as e:
            return False, "", str(e)

    def check_adb_status(self):
        """فحص حالة ADB"""
        def check_thread():
            if os.path.exists(self.adb_path):
                self.adb_status.config(text="✅ ADB موجود وجاهز", fg='#32CD32')
                self.update_status("🚀 ADB جاهز - يمكنك الاتصال بـ GameLoop")
                self.connect_btn.config(state='normal')
            else:
                self.download_adb()

        thread = threading.Thread(target=check_thread, daemon=True)
        thread.start()

    def download_adb(self):
        """تحميل ADB"""
        try:
            self.adb_status.config(text="📥 جاري تحميل ADB...", fg='#FFA500')
            self.update_status("📥 جاري تحميل ADB platform-tools...")

            adb_url = "https://dl.google.com/android/repository/platform-tools-latest-windows.zip"
            zip_file = "platform-tools.zip"

            urllib.request.urlretrieve(adb_url, zip_file)

            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall('.')

            os.remove(zip_file)

            if os.path.exists(self.adb_path):
                self.adb_status.config(text="✅ تم تحميل ADB بنجاح", fg='#32CD32')
                self.update_status("🚀 ADB جاهز - يمكنك الاتصال بـ GameLoop")
                self.connect_btn.config(state='normal')
            else:
                raise Exception("فشل في استخراج ADB")

        except Exception as e:
            self.adb_status.config(text=f"❌ فشل في تحميل ADB: {str(e)}", fg='#FF4500')
            self.update_status(f"❌ خطأ في تحميل ADB: {str(e)}")

    def connect_to_gameloop(self):
        """الاتصال بـ GameLoop"""
        def connect_thread():
            self.update_status("🔍 جاري البحث عن GameLoop...")

            try:
                # البحث عن أجهزة متصلة
                success, output, _ = self.run_adb_command("devices")

                if not success:
                    self.update_status("❌ فشل في تشغيل ADB")
                    return

                # تحليل قائمة الأجهزة
                devices = []
                lines = output.split('\n')[1:]
                for line in lines:
                    if line.strip() and '\t' in line:
                        device_id, status = line.split('\t')
                        if status == 'device':
                            devices.append(device_id)

                if not devices:
                    # محاولة الاتصال بـ GameLoop على المنافذ الشائعة
                    gameloop_ports = ['21503', '21513', '21523', '21533']

                    for port in gameloop_ports:
                        self.update_status(f"🔄 محاولة الاتصال بالمنفذ {port}...")
                        success, _, _ = self.run_adb_command(f"connect 127.0.0.1:{port}")

                        if success:
                            time.sleep(2)
                            success, output, _ = self.run_adb_command("devices")
                            if success and '127.0.0.1:' + port in output:
                                devices.append(f"127.0.0.1:{port}")
                                break

                if devices:
                    device_id = devices[0]
                    self.current_device = device_id

                    # الحصول على معلومات الجهاز
                    success, model, _ = self.run_adb_command(f"-s {device_id} shell getprop ro.product.model")
                    success2, android_version, _ = self.run_adb_command(f"-s {device_id} shell getprop ro.build.version.release")

                    device_info = f"الجهاز: {model if success else 'غير معروف'}"
                    if success2:
                        device_info += f" | Android {android_version}"

                    self.connection_status.config(text=f"✅ متصل: {device_id}", fg='#32CD32')
                    self.device_info.config(text=device_info)

                    # تفعيل أزرار التحكم
                    self.enable_tabs_btn.config(state='normal')
                    self.disable_tabs_btn.config(state='normal')

                    self.update_status("✅ تم الاتصال بنجاح - جاري فحص حالة Show Tabs...")
                    self.check_show_tabs_status()

                else:
                    self.connection_status.config(text="❌ لم يتم العثور على GameLoop", fg='#FF4500')
                    self.device_info.config(text="تأكد من تشغيل GameLoop وتفعيل USB Debugging")
                    self.update_status("❌ لم يتم العثور على GameLoop")

                    messagebox.showwarning("لم يتم العثور على GameLoop",
                                         "تأكد من:\n"
                                         "1. تشغيل GameLoop\n"
                                         "2. تفعيل Developer Options\n"
                                         "3. تفعيل USB Debugging\n"
                                         "4. السماح لـ ADB بالاتصال")

            except Exception as e:
                self.update_status(f"❌ خطأ في الاتصال: {str(e)}")
                messagebox.showerror("خطأ", f"فشل في الاتصال:\n{str(e)}")

        thread = threading.Thread(target=connect_thread, daemon=True)
        thread.start()

    def check_show_tabs_status(self):
        """فحص حالة Show Tabs"""
        if not self.current_device:
            return

        try:
            success, output, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['get_show_taps_status']}")

            if success:
                show_tabs_enabled = output.strip() == '1'
                status_text = "مفعل ✅" if show_tabs_enabled else "غير مفعل ❌"
                color = "#32CD32" if show_tabs_enabled else "#FF4500"

                self.show_tabs_status.config(text=f"📋 Show Tabs: {status_text}", fg=color)

            else:
                self.show_tabs_status.config(text="📋 Show Tabs: خطأ في القراءة", fg='#FFA500')

        except Exception as e:
            print(f"خطأ في فحص حالة Show Tabs: {e}")

    def enable_show_tabs(self):
        """تفعيل Show Tabs"""
        def enable_thread():
            self.update_status("🔄 جاري تفعيل Show Tabs...")

            try:
                success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['enable_show_taps']}")

                if success:
                    self.update_status("✅ تم تفعيل Show Tabs بنجاح!")
                    self.check_show_tabs_status()

                    messagebox.showinfo("نجح التفعيل",
                                      "✅ تم تفعيل Show Tabs بنجاح!\n"
                                      "الآن ستظهر النقاط عند اللمس في GameLoop")
                else:
                    self.update_status(f"❌ فشل في تفعيل Show Tabs: {error}")
                    messagebox.showerror("خطأ", f"فشل في تفعيل Show Tabs:\n{error}")

            except Exception as e:
                self.update_status(f"❌ خطأ: {str(e)}")
                messagebox.showerror("خطأ", f"حدث خطأ:\n{str(e)}")

        thread = threading.Thread(target=enable_thread, daemon=True)
        thread.start()

    def disable_show_tabs(self):
        """إلغاء تفعيل Show Tabs"""
        def disable_thread():
            self.update_status("🔄 جاري إلغاء تفعيل Show Tabs...")

            try:
                success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['disable_show_taps']}")

                if success:
                    self.update_status("✅ تم إلغاء تفعيل Show Tabs بنجاح!")
                    self.check_show_tabs_status()

                    messagebox.showinfo("تم الإلغاء",
                                      "❌ تم إلغاء تفعيل Show Tabs بنجاح!\n"
                                      "لن تظهر النقاط عند اللمس في GameLoop")
                else:
                    self.update_status(f"❌ فشل في إلغاء التفعيل: {error}")
                    messagebox.showerror("خطأ", f"فشل في إلغاء التفعيل:\n{error}")

            except Exception as e:
                self.update_status(f"❌ خطأ: {str(e)}")
                messagebox.showerror("خطأ", f"حدث خطأ:\n{str(e)}")

        thread = threading.Thread(target=disable_thread, daemon=True)
        thread.start()

    def toggle_hide_dots(self):
        """تبديل حالة إخفاء النقاط"""
        if not self.current_device:
            messagebox.showwarning("تحذير", "يرجى الاتصال بـ GameLoop أولاً")
            self.hide_dots_var.set(False)
            return

        # التحقق من أن Show Tabs مفعل
        success, output, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['get_show_taps_status']}")
        if not success or output.strip() != '1':
            messagebox.showwarning("تحذير", "يجب تفعيل Show Tabs أولاً لاستخدام هذا الخيار")
            self.hide_dots_var.set(False)
            return

        def toggle_thread():
            if self.hide_dots_var.get():
                # إخفاء النقاط البيضاء
                methods = [
                    ('🎯 الطريقة الخاصة بـ GameLoop', 'gameloop_ultimate_hide'),
                    ('🔄 طريقة التبديل السريع', 'hide_method_1'),
                    ('🔒 طريقة الإعدادات الآمنة', 'hide_method_2'),
                    ('🌐 طريقة الإعدادات العامة', 'hide_method_3'),
                    ('⚙️ طريقة خصائص النظام', 'hide_method_4'),
                    ('⏱️ طريقة التأخير', 'hide_method_5'),
                    ('🗑️ طريقة حذف الإعدادات', 'hide_method_6'),
                    ('❌ طريقة القيمة الفارغة', 'hide_method_7'),
                    ('🔘 طريقة الشفافية', 'hide_method_8'),
                    ('🔸 طريقة التصغير الشديد', 'hide_method_9'),
                    ('📏 طريقة تقليل الحجم', 'hide_method_10')
                ]

                success = False
                successful_method = ""

                for method_name, method_key in methods:
                    self.update_status(f"🔄 جاري تجربة {method_name}...")

                    success, _, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands[method_key]}")

                    if success:
                        successful_method = method_name
                        self.update_status(f"✅ نجح! تم إخفاء النقاط البيضاء باستخدام {method_name}!")
                        break
                    else:
                        time.sleep(0.3)

                if success:
                    messagebox.showinfo("🎉 نجح الإخفاء!",
                                      f"✅ تم إخفاء النقاط البيضاء بنجاح!\n\n"
                                      f"الطريقة المستخدمة: {successful_method}\n\n"
                                      f"الآن النقاط البيضاء مخفية في GameLoop! 🔘")
                else:
                    self.update_status("⚠️ فشل في إخفاء النقاط بجميع الطرق")
                    self.hide_dots_var.set(False)
                    messagebox.showwarning("فشل الإخفاء",
                                         "❌ فشل في إخفاء النقاط بجميع الطرق\n\n"
                                         "جرب:\n"
                                         "1. إعادة تشغيل GameLoop\n"
                                         "2. إعادة تفعيل Show Tabs\n"
                                         "3. المحاولة مرة أخرى")
            else:
                # إعادة إظهار النقاط
                self.update_status("🔄 جاري إعادة إظهار النقاط...")
                success, _, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['restore_touches']}")

                if success:
                    self.update_status("✅ تم إعادة إظهار النقاط بنجاح!")
                    messagebox.showinfo("تم الإظهار", "✅ تم إعادة إظهار النقاط البيضاء! ⚪")
                else:
                    self.update_status("❌ فشل في إعادة إظهار النقاط")
                    self.hide_dots_var.set(True)

        thread = threading.Thread(target=toggle_thread, daemon=True)
        thread.start()

    def run(self):
        """تشغيل الأداة"""
        self.root.mainloop()


def main():
    """الدالة الرئيسية"""
    app = GameLoopADBPro()
    app.run()


if __name__ == "__main__":
    main()
