#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GameLoop Show Tabs Professional Tool
أداة احترافية لتفعيل Show Tabs في محاكي Game Loop
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import winreg
import os
import sys
import json
import threading
from pathlib import Path
import subprocess

class GameLoopShowTabsTool:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.load_settings()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("GameLoop Show Tabs - أداة احترافية")
        self.root.geometry("600x500")
        self.root.resizable(False, False)

        # تعيين أيقونة النافذة
        try:
            self.root.iconbitmap(default="icon.ico")
        except:
            pass

        # تعيين لون الخلفية
        self.root.configure(bg='#1e1e1e')

        # وضع النافذة في المنتصف
        self.center_window()

    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"600x500+{x}+{y}")

    def setup_styles(self):
        """إعداد الأنماط الاحترافية"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # تخصيص الألوان
        self.colors = {
            'bg': '#1e1e1e',
            'fg': '#ffffff',
            'accent': '#0078d4',
            'success': '#107c10',
            'warning': '#ff8c00',
            'error': '#d13438',
            'button_bg': '#2d2d30',
            'button_hover': '#3e3e42'
        }

        # تخصيص أنماط الأزرار
        self.style.configure('Custom.TButton',
                           background=self.colors['button_bg'],
                           foreground=self.colors['fg'],
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 10))

        self.style.map('Custom.TButton',
                      background=[('active', self.colors['button_hover']),
                                ('pressed', self.colors['accent'])])

        # تخصيص أنماط الإطارات
        self.style.configure('Custom.TFrame',
                           background=self.colors['bg'],
                           borderwidth=1,
                           relief='solid')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, style='Custom.TFrame')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # العنوان الرئيسي
        title_label = tk.Label(main_frame,
                              text="🎮 GameLoop Show Tabs Tool",
                              font=('Segoe UI', 18, 'bold'),
                              bg=self.colors['bg'],
                              fg=self.colors['accent'])
        title_label.pack(pady=(0, 20))

        # وصف الأداة
        desc_label = tk.Label(main_frame,
                             text="أداة احترافية لتفعيل Show Tabs في محاكي Game Loop\nبدون إظهار النقاط البيضاء",
                             font=('Segoe UI', 10),
                             bg=self.colors['bg'],
                             fg=self.colors['fg'],
                             justify='center')
        desc_label.pack(pady=(0, 30))

        # إطار معلومات الحالة
        status_frame = ttk.Frame(main_frame, style='Custom.TFrame')
        status_frame.pack(fill='x', pady=(0, 20))

        # حالة GameLoop
        self.gameloop_status_label = tk.Label(status_frame,
                                            text="🔍 فحص حالة GameLoop...",
                                            font=('Segoe UI', 10),
                                            bg=self.colors['bg'],
                                            fg=self.colors['warning'])
        self.gameloop_status_label.pack(pady=5)

        # حالة Show Tabs
        self.tabs_status_label = tk.Label(status_frame,
                                        text="📋 حالة Show Tabs: غير معروف",
                                        font=('Segoe UI', 10),
                                        bg=self.colors['bg'],
                                        fg=self.colors['fg'])
        self.tabs_status_label.pack(pady=5)

        # إطار الأزرار الرئيسية
        buttons_frame = ttk.Frame(main_frame, style='Custom.TFrame')
        buttons_frame.pack(fill='x', pady=20)

        # زر فحص GameLoop
        self.scan_button = ttk.Button(buttons_frame,
                                    text="🔍 فحص GameLoop",
                                    style='Custom.TButton',
                                    command=self.scan_gameloop)
        self.scan_button.pack(side='left', padx=(0, 10), fill='x', expand=True)

        # زر تفعيل Show Tabs
        self.enable_button = ttk.Button(buttons_frame,
                                      text="✅ تفعيل Show Tabs",
                                      style='Custom.TButton',
                                      command=self.enable_show_tabs,
                                      state='disabled')
        self.enable_button.pack(side='left', padx=5, fill='x', expand=True)

        # زر إلغاء التفعيل
        self.disable_button = ttk.Button(buttons_frame,
                                       text="❌ إلغاء التفعيل",
                                       style='Custom.TButton',
                                       command=self.disable_show_tabs,
                                       state='disabled')
        self.disable_button.pack(side='left', padx=(10, 0), fill='x', expand=True)

        # إطار الخيارات المتقدمة
        options_frame = ttk.LabelFrame(main_frame, text="خيارات متقدمة", style='Custom.TFrame')
        options_frame.pack(fill='x', pady=20)

        # خيار إخفاء النقاط البيضاء
        self.hide_dots_var = tk.BooleanVar(value=True)
        hide_dots_check = tk.Checkbutton(options_frame,
                                       text="إخفاء النقاط البيضاء",
                                       variable=self.hide_dots_var,
                                       bg=self.colors['bg'],
                                       fg=self.colors['fg'],
                                       selectcolor=self.colors['button_bg'],
                                       activebackground=self.colors['bg'],
                                       activeforeground=self.colors['fg'],
                                       font=('Segoe UI', 10))
        hide_dots_check.pack(anchor='w', padx=10, pady=5)

        # خيار التشغيل التلقائي
        self.auto_start_var = tk.BooleanVar()
        auto_start_check = tk.Checkbutton(options_frame,
                                        text="تفعيل تلقائي عند بدء GameLoop",
                                        variable=self.auto_start_var,
                                        bg=self.colors['bg'],
                                        fg=self.colors['fg'],
                                        selectcolor=self.colors['button_bg'],
                                        activebackground=self.colors['bg'],
                                        activeforeground=self.colors['fg'],
                                        font=('Segoe UI', 10))
        auto_start_check.pack(anchor='w', padx=10, pady=5)

        # شريط التقدم
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill='x', pady=10)

        # شريط الحالة
        self.status_bar = tk.Label(main_frame,
                                 text="جاهز للاستخدام",
                                 font=('Segoe UI', 9),
                                 bg=self.colors['button_bg'],
                                 fg=self.colors['fg'],
                                 relief='sunken',
                                 anchor='w')
        self.status_bar.pack(fill='x', side='bottom')

        # بدء فحص GameLoop تلقائياً
        self.root.after(1000, self.scan_gameloop)

    def update_status(self, message, color=None):
        """تحديث شريط الحالة"""
        if color is None:
            color = self.colors['fg']
        self.status_bar.config(text=message, fg=color)
        self.root.update()

    def scan_gameloop(self):
        """فحص وجود وحالة GameLoop"""
        def scan_thread():
            self.progress.start()
            self.update_status("جاري فحص GameLoop...", self.colors['warning'])

            try:
                # البحث عن GameLoop في المسارات الشائعة
                gameloop_paths = [
                    os.path.expandvars(r"%PROGRAMFILES%\TxGameAssistant"),
                    os.path.expandvars(r"%PROGRAMFILES(X86)%\TxGameAssistant"),
                    os.path.expandvars(r"%LOCALAPPDATA%\TxGameAssistant"),
                    r"C:\Program Files\TxGameAssistant",
                    r"C:\Program Files (x86)\TxGameAssistant"
                ]

                gameloop_found = False
                gameloop_path = None

                for path in gameloop_paths:
                    if os.path.exists(path):
                        gameloop_found = True
                        gameloop_path = path
                        break

                if gameloop_found:
                    self.gameloop_status_label.config(
                        text=f"✅ GameLoop موجود: {gameloop_path}",
                        fg=self.colors['success']
                    )
                    self.enable_button.config(state='normal')
                    self.disable_button.config(state='normal')
                    self.update_status("GameLoop تم العثور عليه بنجاح", self.colors['success'])

                    # فحص حالة Show Tabs الحالية
                    self.check_tabs_status()
                else:
                    self.gameloop_status_label.config(
                        text="❌ GameLoop غير موجود",
                        fg=self.colors['error']
                    )
                    self.update_status("لم يتم العثور على GameLoop", self.colors['error'])

            except Exception as e:
                self.gameloop_status_label.config(
                    text=f"❌ خطأ في الفحص: {str(e)}",
                    fg=self.colors['error']
                )
                self.update_status(f"خطأ: {str(e)}", self.colors['error'])

            finally:
                self.progress.stop()

        threading.Thread(target=scan_thread, daemon=True).start()

    def check_tabs_status(self):
        """فحص حالة Show Tabs الحالية"""
        try:
            # فحص إعدادات GameLoop في الريجستري
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                              r"SOFTWARE\TxGameAssistant", 0,
                              winreg.KEY_READ) as key:
                try:
                    show_tabs_value = winreg.QueryValueEx(key, "ShowTabs")[0]
                    if show_tabs_value == 1:
                        self.tabs_status_label.config(
                            text="📋 Show Tabs: مفعل ✅",
                            fg=self.colors['success']
                        )
                    else:
                        self.tabs_status_label.config(
                            text="📋 Show Tabs: غير مفعل ❌",
                            fg=self.colors['error']
                        )
                except FileNotFoundError:
                    self.tabs_status_label.config(
                        text="📋 Show Tabs: غير محدد",
                        fg=self.colors['warning']
                    )
        except Exception as e:
            self.tabs_status_label.config(
                text="📋 Show Tabs: خطأ في القراءة",
                fg=self.colors['error']
            )

    def enable_show_tabs(self):
        """تفعيل Show Tabs"""
        def enable_thread():
            self.progress.start()
            self.update_status("جاري تفعيل Show Tabs...", self.colors['warning'])

            try:
                # إنشاء أو تعديل مفتاح الريجستري
                with winreg.CreateKey(winreg.HKEY_CURRENT_USER,
                                    r"SOFTWARE\TxGameAssistant") as key:
                    # تفعيل Show Tabs
                    winreg.SetValueEx(key, "ShowTabs", 0, winreg.REG_DWORD, 1)

                    # إخفاء النقاط البيضاء إذا كان مفعلاً
                    if self.hide_dots_var.get():
                        winreg.SetValueEx(key, "HideWhiteDots", 0, winreg.REG_DWORD, 1)
                        winreg.SetValueEx(key, "TabsVisualEffects", 0, winreg.REG_DWORD, 0)

                    # إعدادات إضافية لتحسين الأداء
                    winreg.SetValueEx(key, "TabsAnimation", 0, winreg.REG_DWORD, 0)
                    winreg.SetValueEx(key, "TabsTransparency", 0, winreg.REG_DWORD, 1)

                # تحديث الحالة
                self.check_tabs_status()
                self.update_status("تم تفعيل Show Tabs بنجاح!", self.colors['success'])

                # إظهار رسالة نجاح
                messagebox.showinfo("نجح التفعيل",
                                  "تم تفعيل Show Tabs بنجاح!\n"
                                  "قم بإعادة تشغيل GameLoop لتطبيق التغييرات.")

            except Exception as e:
                self.update_status(f"خطأ في التفعيل: {str(e)}", self.colors['error'])
                messagebox.showerror("خطأ", f"فشل في تفعيل Show Tabs:\n{str(e)}")

            finally:
                self.progress.stop()

        threading.Thread(target=enable_thread, daemon=True).start()

    def disable_show_tabs(self):
        """إلغاء تفعيل Show Tabs"""
        def disable_thread():
            self.progress.start()
            self.update_status("جاري إلغاء تفعيل Show Tabs...", self.colors['warning'])

            try:
                with winreg.CreateKey(winreg.HKEY_CURRENT_USER,
                                    r"SOFTWARE\TxGameAssistant") as key:
                    # إلغاء تفعيل Show Tabs
                    winreg.SetValueEx(key, "ShowTabs", 0, winreg.REG_DWORD, 0)
                    winreg.SetValueEx(key, "HideWhiteDots", 0, winreg.REG_DWORD, 0)
                    winreg.SetValueEx(key, "TabsVisualEffects", 0, winreg.REG_DWORD, 1)

                # تحديث الحالة
                self.check_tabs_status()
                self.update_status("تم إلغاء تفعيل Show Tabs", self.colors['success'])

                messagebox.showinfo("تم الإلغاء",
                                  "تم إلغاء تفعيل Show Tabs بنجاح!\n"
                                  "قم بإعادة تشغيل GameLoop لتطبيق التغييرات.")

            except Exception as e:
                self.update_status(f"خطأ في الإلغاء: {str(e)}", self.colors['error'])
                messagebox.showerror("خطأ", f"فشل في إلغاء التفعيل:\n{str(e)}")

            finally:
                self.progress.stop()

        threading.Thread(target=disable_thread, daemon=True).start()

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        settings_file = "gameloop_settings.json"
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.hide_dots_var.set(settings.get('hide_dots', True))
                    self.auto_start_var.set(settings.get('auto_start', False))
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        settings_file = "gameloop_settings.json"
        try:
            settings = {
                'hide_dots': self.hide_dots_var.get(),
                'auto_start': self.auto_start_var.get()
            }
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.save_settings()
        self.root.destroy()

    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من نظام التشغيل
        if sys.platform != "win32":
            messagebox.showerror("خطأ", "هذه الأداة تعمل على Windows فقط")
            return

        # إنشاء وتشغيل التطبيق
        app = GameLoopShowTabsTool()
        app.run()

    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
