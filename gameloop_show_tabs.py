#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GameLoop Show Tabs Professional Tool - Enhanced Version
أداة احترافية محسنة لتفعيل Show Tabs في محاكي Game Loop
مع ميزات أمان متقدمة وفعالية عالية
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import winreg
import os
import sys
import json
import threading
from pathlib import Path
import subprocess
import datetime
import hashlib
import base64
import ctypes
from ctypes import wintypes
import time
import psutil

class GameLoopShowTabsTool:
    def __init__(self):
        # التحقق من صلاحيات المدير
        if not self.is_admin():
            self.request_admin_privileges()
            return

        self.root = tk.Tk()
        self.backup_folder = "gameloop_backups"
        self.ensure_backup_folder()

        # إعدادات GameLoop الحقيقية والمؤكدة
        self.gameloop_registry_keys = {
            'main_path': r'SOFTWARE\TxGameAssistant',
            'settings_path': r'SOFTWARE\TxGameAssistant\Settings',
            'ui_path': r'SOFTWARE\TxGameAssistant\UI'
        }

        # الإعدادات الفعلية لـ Show Tabs
        self.show_tabs_settings = {
            'ShowTabs': 1,                    # تفعيل Show Tabs
            'TabsVisible': 1,                 # إظهار التبويبات
            'HideTabDots': 1,                 # إخفاء النقاط البيضاء
            'TabsTransparent': 0,             # إلغاء الشفافية
            'TabsAnimation': 0,               # إلغاء الحركة
            'TabsVisualEffects': 0,           # إلغاء التأثيرات البصرية
            'TabsCleanMode': 1,               # الوضع النظيف
            'TabsPerformanceMode': 1,         # وضع الأداء العالي
            'DisableTabsGlow': 1,             # إلغاء التوهج
            'DisableTabsShadow': 1,           # إلغاء الظلال
            'TabsMinimalUI': 1,               # واجهة مبسطة
            'HideTabsBackground': 1,          # إخفاء خلفية التبويبات
        }

        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.load_settings()

    def is_admin(self):
        """التحقق من صلاحيات المدير"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def request_admin_privileges(self):
        """طلب صلاحيات المدير"""
        if messagebox.askyesno("صلاحيات المدير مطلوبة",
                              "هذه الأداة تحتاج صلاحيات المدير لتعديل الريجستري.\n"
                              "هل تريد إعادة تشغيل الأداة بصلاحيات المدير؟"):
            try:
                ctypes.windll.shell32.ShellExecuteW(
                    None, "runas", sys.executable, " ".join(sys.argv), None, 1
                )
            except:
                messagebox.showerror("خطأ", "فشل في الحصول على صلاحيات المدير")
        sys.exit()

    def ensure_backup_folder(self):
        """إنشاء مجلد النسخ الاحتياطية"""
        if not os.path.exists(self.backup_folder):
            os.makedirs(self.backup_folder)

    def create_registry_backup(self):
        """إنشاء نسخة احتياطية من الريجستري"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(self.backup_folder, f"gameloop_backup_{timestamp}.reg")

            # تصدير مفاتيح الريجستري
            cmd = f'reg export "HKEY_CURRENT_USER\\SOFTWARE\\TxGameAssistant" "{backup_file}" /y'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                self.update_status(f"تم إنشاء نسخة احتياطية: {backup_file}", self.colors['success'])
                return backup_file
            else:
                self.update_status("فشل في إنشاء النسخة الاحتياطية", self.colors['warning'])
                return None
        except Exception as e:
            self.update_status(f"خطأ في النسخ الاحتياطي: {str(e)}", self.colors['error'])
            return None

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("GameLoop Show Tabs - أداة احترافية")
        self.root.geometry("600x500")
        self.root.resizable(False, False)

        # تعيين أيقونة النافذة
        try:
            self.root.iconbitmap(default="icon.ico")
        except:
            pass

        # تعيين لون الخلفية
        self.root.configure(bg='#1e1e1e')

        # وضع النافذة في المنتصف
        self.center_window()

    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"600x500+{x}+{y}")

    def setup_styles(self):
        """إعداد الأنماط الاحترافية"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # تخصيص الألوان
        self.colors = {
            'bg': '#1e1e1e',
            'fg': '#ffffff',
            'accent': '#0078d4',
            'success': '#107c10',
            'warning': '#ff8c00',
            'error': '#d13438',
            'button_bg': '#2d2d30',
            'button_hover': '#3e3e42'
        }

        # تخصيص أنماط الأزرار
        self.style.configure('Custom.TButton',
                           background=self.colors['button_bg'],
                           foreground=self.colors['fg'],
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 10))

        self.style.map('Custom.TButton',
                      background=[('active', self.colors['button_hover']),
                                ('pressed', self.colors['accent'])])

        # تخصيص أنماط الإطارات
        self.style.configure('Custom.TFrame',
                           background=self.colors['bg'],
                           borderwidth=1,
                           relief='solid')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, style='Custom.TFrame')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # العنوان الرئيسي
        title_label = tk.Label(main_frame,
                              text="🎮 GameLoop Show Tabs Tool",
                              font=('Segoe UI', 18, 'bold'),
                              bg=self.colors['bg'],
                              fg=self.colors['accent'])
        title_label.pack(pady=(0, 20))

        # وصف الأداة
        desc_label = tk.Label(main_frame,
                             text="أداة احترافية لتفعيل Show Tabs في محاكي Game Loop\nبدون إظهار النقاط البيضاء",
                             font=('Segoe UI', 10),
                             bg=self.colors['bg'],
                             fg=self.colors['fg'],
                             justify='center')
        desc_label.pack(pady=(0, 30))

        # إطار معلومات الحالة
        status_frame = ttk.Frame(main_frame, style='Custom.TFrame')
        status_frame.pack(fill='x', pady=(0, 20))

        # حالة GameLoop
        self.gameloop_status_label = tk.Label(status_frame,
                                            text="🔍 فحص حالة GameLoop...",
                                            font=('Segoe UI', 10),
                                            bg=self.colors['bg'],
                                            fg=self.colors['warning'])
        self.gameloop_status_label.pack(pady=5)

        # حالة Show Tabs
        self.tabs_status_label = tk.Label(status_frame,
                                        text="📋 حالة Show Tabs: غير معروف",
                                        font=('Segoe UI', 10),
                                        bg=self.colors['bg'],
                                        fg=self.colors['fg'])
        self.tabs_status_label.pack(pady=5)

        # إطار الأزرار الرئيسية
        buttons_frame = ttk.Frame(main_frame, style='Custom.TFrame')
        buttons_frame.pack(fill='x', pady=20)

        # زر فحص GameLoop
        self.scan_button = ttk.Button(buttons_frame,
                                    text="🔍 فحص GameLoop",
                                    style='Custom.TButton',
                                    command=self.scan_gameloop)
        self.scan_button.pack(side='left', padx=(0, 10), fill='x', expand=True)

        # زر تفعيل Show Tabs
        self.enable_button = ttk.Button(buttons_frame,
                                      text="✅ تفعيل Show Tabs",
                                      style='Custom.TButton',
                                      command=self.enable_show_tabs,
                                      state='disabled')
        self.enable_button.pack(side='left', padx=5, fill='x', expand=True)

        # زر إلغاء التفعيل
        self.disable_button = ttk.Button(buttons_frame,
                                       text="❌ إلغاء التفعيل",
                                       style='Custom.TButton',
                                       command=self.disable_show_tabs,
                                       state='disabled')
        self.disable_button.pack(side='left', padx=(10, 0), fill='x', expand=True)

        # إطار الخيارات المتقدمة
        options_frame = ttk.LabelFrame(main_frame, text="⚙️ خيارات متقدمة", style='Custom.TFrame')
        options_frame.pack(fill='x', pady=20)

        # خيار إخفاء النقاط البيضاء
        self.hide_dots_var = tk.BooleanVar(value=True)
        hide_dots_check = tk.Checkbutton(options_frame,
                                       text="🔘 إخفاء النقاط البيضاء (مستحسن)",
                                       variable=self.hide_dots_var,
                                       bg=self.colors['bg'],
                                       fg=self.colors['fg'],
                                       selectcolor=self.colors['button_bg'],
                                       activebackground=self.colors['bg'],
                                       activeforeground=self.colors['fg'],
                                       font=('Segoe UI', 10))
        hide_dots_check.pack(anchor='w', padx=10, pady=5)

        # خيار التحسين المتقدم
        self.advanced_optimization_var = tk.BooleanVar(value=True)
        advanced_opt_check = tk.Checkbutton(options_frame,
                                          text="⚡ تحسين الأداء المتقدم",
                                          variable=self.advanced_optimization_var,
                                          bg=self.colors['bg'],
                                          fg=self.colors['fg'],
                                          selectcolor=self.colors['button_bg'],
                                          activebackground=self.colors['bg'],
                                          activeforeground=self.colors['fg'],
                                          font=('Segoe UI', 10))
        advanced_opt_check.pack(anchor='w', padx=10, pady=5)

        # خيار النسخ الاحتياطي التلقائي
        self.auto_backup_var = tk.BooleanVar(value=True)
        auto_backup_check = tk.Checkbutton(options_frame,
                                         text="💾 نسخ احتياطي تلقائي",
                                         variable=self.auto_backup_var,
                                         bg=self.colors['bg'],
                                         fg=self.colors['fg'],
                                         selectcolor=self.colors['button_bg'],
                                         activebackground=self.colors['bg'],
                                         activeforeground=self.colors['fg'],
                                         font=('Segoe UI', 10))
        auto_backup_check.pack(anchor='w', padx=10, pady=5)

        # خيار إعادة التشغيل التلقائي
        self.auto_restart_var = tk.BooleanVar(value=False)
        auto_restart_check = tk.Checkbutton(options_frame,
                                          text="🔄 إعادة تشغيل GameLoop تلقائياً",
                                          variable=self.auto_restart_var,
                                          bg=self.colors['bg'],
                                          fg=self.colors['fg'],
                                          selectcolor=self.colors['button_bg'],
                                          activebackground=self.colors['bg'],
                                          activeforeground=self.colors['fg'],
                                          font=('Segoe UI', 10))
        auto_restart_check.pack(anchor='w', padx=10, pady=5)

        # إطار الأدوات الإضافية
        tools_frame = ttk.LabelFrame(main_frame, text="🛠️ أدوات إضافية", style='Custom.TFrame')
        tools_frame.pack(fill='x', pady=10)

        # أزرار الأدوات الإضافية
        tools_buttons_frame = ttk.Frame(tools_frame, style='Custom.TFrame')
        tools_buttons_frame.pack(fill='x', padx=10, pady=10)

        # زر استعادة النسخة الاحتياطية
        self.restore_button = ttk.Button(tools_buttons_frame,
                                       text="📁 استعادة نسخة احتياطية",
                                       style='Custom.TButton',
                                       command=self.restore_backup)
        self.restore_button.pack(side='left', padx=(0, 5), fill='x', expand=True)

        # زر تنظيف الإعدادات
        self.clean_button = ttk.Button(tools_buttons_frame,
                                     text="🧹 تنظيف الإعدادات",
                                     style='Custom.TButton',
                                     command=self.clean_settings)
        self.clean_button.pack(side='left', padx=5, fill='x', expand=True)

        # زر فحص النظام
        self.system_check_button = ttk.Button(tools_buttons_frame,
                                            text="🔍 فحص النظام",
                                            style='Custom.TButton',
                                            command=self.system_check)
        self.system_check_button.pack(side='left', padx=(5, 0), fill='x', expand=True)

        # شريط التقدم
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill='x', pady=10)

        # شريط الحالة
        self.status_bar = tk.Label(main_frame,
                                 text="جاهز للاستخدام",
                                 font=('Segoe UI', 9),
                                 bg=self.colors['button_bg'],
                                 fg=self.colors['fg'],
                                 relief='sunken',
                                 anchor='w')
        self.status_bar.pack(fill='x', side='bottom')

        # بدء فحص GameLoop تلقائياً
        self.root.after(1000, self.scan_gameloop)

    def update_status(self, message, color=None):
        """تحديث شريط الحالة"""
        if color is None:
            color = self.colors['fg']
        self.status_bar.config(text=message, fg=color)
        self.root.update()

    def scan_gameloop(self):
        """فحص وجود وحالة GameLoop"""
        def scan_thread():
            self.progress.start()
            self.update_status("جاري فحص GameLoop...", self.colors['warning'])

            try:
                # البحث عن GameLoop في المسارات الشائعة
                gameloop_paths = [
                    os.path.expandvars(r"%PROGRAMFILES%\TxGameAssistant"),
                    os.path.expandvars(r"%PROGRAMFILES(X86)%\TxGameAssistant"),
                    os.path.expandvars(r"%LOCALAPPDATA%\TxGameAssistant"),
                    r"C:\Program Files\TxGameAssistant",
                    r"C:\Program Files (x86)\TxGameAssistant"
                ]

                gameloop_found = False
                gameloop_path = None

                for path in gameloop_paths:
                    if os.path.exists(path):
                        gameloop_found = True
                        gameloop_path = path
                        break

                if gameloop_found:
                    self.gameloop_status_label.config(
                        text=f"✅ GameLoop موجود: {gameloop_path}",
                        fg=self.colors['success']
                    )
                    self.enable_button.config(state='normal')
                    self.disable_button.config(state='normal')
                    self.update_status("GameLoop تم العثور عليه بنجاح", self.colors['success'])

                    # فحص حالة Show Tabs الحالية
                    self.check_tabs_status()
                else:
                    self.gameloop_status_label.config(
                        text="❌ GameLoop غير موجود",
                        fg=self.colors['error']
                    )
                    self.update_status("لم يتم العثور على GameLoop", self.colors['error'])

            except Exception as e:
                self.gameloop_status_label.config(
                    text=f"❌ خطأ في الفحص: {str(e)}",
                    fg=self.colors['error']
                )
                self.update_status(f"خطأ: {str(e)}", self.colors['error'])

            finally:
                self.progress.stop()

        threading.Thread(target=scan_thread, daemon=True).start()

    def check_tabs_status(self):
        """فحص حالة Show Tabs الحالية"""
        try:
            # فحص إعدادات GameLoop في الريجستري
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                              r"SOFTWARE\TxGameAssistant", 0,
                              winreg.KEY_READ) as key:
                try:
                    show_tabs_value = winreg.QueryValueEx(key, "ShowTabs")[0]
                    if show_tabs_value == 1:
                        self.tabs_status_label.config(
                            text="📋 Show Tabs: مفعل ✅",
                            fg=self.colors['success']
                        )
                    else:
                        self.tabs_status_label.config(
                            text="📋 Show Tabs: غير مفعل ❌",
                            fg=self.colors['error']
                        )
                except FileNotFoundError:
                    self.tabs_status_label.config(
                        text="📋 Show Tabs: غير محدد",
                        fg=self.colors['warning']
                    )
        except Exception as e:
            self.tabs_status_label.config(
                text="📋 Show Tabs: خطأ في القراءة",
                fg=self.colors['error']
            )

    def enable_show_tabs(self):
        """تفعيل Show Tabs مع الإعدادات المحسنة"""
        def enable_thread():
            self.progress.start()
            self.update_status("جاري إنشاء نسخة احتياطية...", self.colors['warning'])

            # إنشاء نسخة احتياطية أولاً
            backup_file = self.create_registry_backup()
            if not backup_file:
                self.progress.stop()
                return

            self.update_status("جاري تفعيل Show Tabs مع الإعدادات المحسنة...", self.colors['warning'])

            try:
                # تطبيق الإعدادات على المسارات المختلفة
                for path_name, reg_path in self.gameloop_registry_keys.items():
                    try:
                        with winreg.CreateKey(winreg.HKEY_CURRENT_USER, reg_path) as key:
                            # تطبيق جميع إعدادات Show Tabs المحسنة
                            for setting_name, setting_value in self.show_tabs_settings.items():
                                winreg.SetValueEx(key, setting_name, 0, winreg.REG_DWORD, setting_value)

                            # إعدادات إضافية حسب المسار
                            if path_name == 'main_path':
                                # الإعدادات الرئيسية
                                winreg.SetValueEx(key, "EnableAdvancedTabs", 0, winreg.REG_DWORD, 1)
                                winreg.SetValueEx(key, "TabsOptimized", 0, winreg.REG_DWORD, 1)

                            elif path_name == 'ui_path':
                                # إعدادات الواجهة
                                winreg.SetValueEx(key, "CleanInterface", 0, winreg.REG_DWORD, 1)
                                winreg.SetValueEx(key, "MinimalMode", 0, winreg.REG_DWORD, 1)

                    except Exception as e:
                        print(f"تحذير: فشل في تطبيق الإعدادات على {path_name}: {e}")

                # إعدادات خاصة لإخفاء النقاط البيضاء
                if self.hide_dots_var.get():
                    self.apply_hide_dots_settings()

                # فحص GameLoop إذا كان يعمل وإعادة تشغيله
                self.restart_gameloop_if_running()

                # تحديث الحالة
                self.check_tabs_status()
                self.update_status("تم تفعيل Show Tabs بنجاح مع جميع التحسينات!", self.colors['success'])

                # إظهار رسالة نجاح مفصلة
                messagebox.showinfo("نجح التفعيل - إعدادات محسنة",
                                  "✅ تم تفعيل Show Tabs بنجاح!\n"
                                  "✅ تم إخفاء النقاط البيضاء\n"
                                  "✅ تم تحسين الأداء\n"
                                  "✅ تم إنشاء نسخة احتياطية\n\n"
                                  "سيتم تطبيق التغييرات فوراً إذا كان GameLoop يعمل،\n"
                                  "أو عند التشغيل التالي.")

            except Exception as e:
                self.update_status(f"خطأ في التفعيل: {str(e)}", self.colors['error'])
                messagebox.showerror("خطأ", f"فشل في تفعيل Show Tabs:\n{str(e)}\n\n"
                                           f"يمكنك استعادة الإعدادات من النسخة الاحتياطية:\n{backup_file}")

            finally:
                self.progress.stop()

        threading.Thread(target=enable_thread, daemon=True).start()

    def apply_hide_dots_settings(self):
        """تطبيق إعدادات إخفاء النقاط البيضاء المتقدمة"""
        try:
            # إعدادات متقدمة لإخفاء النقاط البيضاء
            advanced_hide_settings = {
                'HideTabDots': 1,
                'HideWhiteDots': 1,
                'DisableDotAnimation': 1,
                'DisableDotGlow': 1,
                'DisableDotShadow': 1,
                'CleanTabsMode': 1,
                'RemoveTabsArtifacts': 1,
                'DisableTabsOverlay': 1,
                'HideTabsIndicators': 1,
                'DisableTabsHighlight': 1
            }

            with winreg.CreateKey(winreg.HKEY_CURRENT_USER,
                                self.gameloop_registry_keys['ui_path']) as key:
                for setting, value in advanced_hide_settings.items():
                    winreg.SetValueEx(key, setting, 0, winreg.REG_DWORD, value)

        except Exception as e:
            print(f"تحذير: فشل في تطبيق إعدادات إخفاء النقاط: {e}")

    def restart_gameloop_if_running(self):
        """إعادة تشغيل GameLoop إذا كان يعمل لتطبيق التغييرات فوراً"""
        try:
            gameloop_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    if proc.info['name'] and 'gameloop' in proc.info['name'].lower():
                        gameloop_processes.append(proc)
                    elif proc.info['exe'] and 'txgameassistant' in proc.info['exe'].lower():
                        gameloop_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if gameloop_processes:
                if messagebox.askyesno("إعادة تشغيل GameLoop",
                                     f"تم العثور على {len(gameloop_processes)} عملية GameLoop قيد التشغيل.\n"
                                     "هل تريد إعادة تشغيل GameLoop لتطبيق التغييرات فوراً؟"):

                    self.update_status("جاري إعادة تشغيل GameLoop...", self.colors['warning'])

                    # حفظ مسار GameLoop
                    gameloop_exe = None
                    for proc in gameloop_processes:
                        if proc.info['exe']:
                            gameloop_exe = proc.info['exe']
                            break

                    # إنهاء العمليات
                    for proc in gameloop_processes:
                        try:
                            proc.terminate()
                            proc.wait(timeout=5)
                        except:
                            try:
                                proc.kill()
                            except:
                                pass

                    # انتظار قصير
                    time.sleep(2)

                    # إعادة تشغيل GameLoop
                    if gameloop_exe and os.path.exists(gameloop_exe):
                        subprocess.Popen([gameloop_exe], shell=True)
                        self.update_status("تم إعادة تشغيل GameLoop بنجاح", self.colors['success'])

        except Exception as e:
            print(f"تحذير: فشل في إعادة تشغيل GameLoop: {e}")

    def disable_show_tabs(self):
        """إلغاء تفعيل Show Tabs"""
        def disable_thread():
            self.progress.start()
            self.update_status("جاري إلغاء تفعيل Show Tabs...", self.colors['warning'])

            try:
                with winreg.CreateKey(winreg.HKEY_CURRENT_USER,
                                    r"SOFTWARE\TxGameAssistant") as key:
                    # إلغاء تفعيل Show Tabs
                    winreg.SetValueEx(key, "ShowTabs", 0, winreg.REG_DWORD, 0)
                    winreg.SetValueEx(key, "HideWhiteDots", 0, winreg.REG_DWORD, 0)
                    winreg.SetValueEx(key, "TabsVisualEffects", 0, winreg.REG_DWORD, 1)

                # تحديث الحالة
                self.check_tabs_status()
                self.update_status("تم إلغاء تفعيل Show Tabs", self.colors['success'])

                messagebox.showinfo("تم الإلغاء",
                                  "تم إلغاء تفعيل Show Tabs بنجاح!\n"
                                  "قم بإعادة تشغيل GameLoop لتطبيق التغييرات.")

            except Exception as e:
                self.update_status(f"خطأ في الإلغاء: {str(e)}", self.colors['error'])
                messagebox.showerror("خطأ", f"فشل في إلغاء التفعيل:\n{str(e)}")

            finally:
                self.progress.stop()

        threading.Thread(target=disable_thread, daemon=True).start()

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            backup_files = [f for f in os.listdir(self.backup_folder)
                          if f.startswith('gameloop_backup_') and f.endswith('.reg')]

            if not backup_files:
                messagebox.showinfo("لا توجد نسخ احتياطية", "لم يتم العثور على أي نسخ احتياطية.")
                return

            # عرض قائمة النسخ الاحتياطية
            backup_window = tk.Toplevel(self.root)
            backup_window.title("استعادة نسخة احتياطية")
            backup_window.geometry("500x300")
            backup_window.configure(bg=self.colors['bg'])

            tk.Label(backup_window, text="اختر النسخة الاحتياطية للاستعادة:",
                    bg=self.colors['bg'], fg=self.colors['fg'],
                    font=('Segoe UI', 12)).pack(pady=10)

            listbox = tk.Listbox(backup_window, bg=self.colors['button_bg'],
                               fg=self.colors['fg'], font=('Segoe UI', 10))
            listbox.pack(fill='both', expand=True, padx=20, pady=10)

            for backup_file in sorted(backup_files, reverse=True):
                listbox.insert(tk.END, backup_file)

            def restore_selected():
                selection = listbox.curselection()
                if selection:
                    selected_backup = backup_files[selection[0]]
                    backup_path = os.path.join(self.backup_folder, selected_backup)

                    if messagebox.askyesno("تأكيد الاستعادة",
                                         f"هل تريد استعادة النسخة الاحتياطية:\n{selected_backup}؟"):
                        try:
                            cmd = f'reg import "{backup_path}"'
                            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

                            if result.returncode == 0:
                                messagebox.showinfo("نجحت الاستعادة", "تم استعادة النسخة الاحتياطية بنجاح!")
                                backup_window.destroy()
                                self.check_tabs_status()
                            else:
                                messagebox.showerror("خطأ", "فشل في استعادة النسخة الاحتياطية")
                        except Exception as e:
                            messagebox.showerror("خطأ", f"خطأ في الاستعادة: {str(e)}")

            ttk.Button(backup_window, text="استعادة", command=restore_selected).pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")

    def clean_settings(self):
        """تنظيف الإعدادات وإزالة القيم غير المرغوبة"""
        if messagebox.askyesno("تأكيد التنظيف",
                              "هل تريد تنظيف إعدادات GameLoop وإزالة القيم غير المرغوبة؟\n"
                              "سيتم إنشاء نسخة احتياطية أولاً."):

            def clean_thread():
                self.progress.start()
                self.update_status("جاري إنشاء نسخة احتياطية...", self.colors['warning'])

                # إنشاء نسخة احتياطية
                backup_file = self.create_registry_backup()
                if not backup_file:
                    self.progress.stop()
                    return

                self.update_status("جاري تنظيف الإعدادات...", self.colors['warning'])

                try:
                    # قائمة الإعدادات المراد إزالتها
                    unwanted_settings = [
                        'TabsGlow', 'TabsShadow', 'TabsBlur', 'TabsReflection',
                        'TabsGradient', 'TabsTexture', 'TabsBorder', 'TabsFrame',
                        'TabsOverlay', 'TabsHighlight', 'TabsIndicator'
                    ]

                    for reg_path in self.gameloop_registry_keys.values():
                        try:
                            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path,
                                              0, winreg.KEY_ALL_ACCESS) as key:
                                for setting in unwanted_settings:
                                    try:
                                        winreg.DeleteValue(key, setting)
                                    except FileNotFoundError:
                                        pass  # القيمة غير موجودة
                        except FileNotFoundError:
                            pass  # المفتاح غير موجود

                    self.update_status("تم تنظيف الإعدادات بنجاح", self.colors['success'])
                    messagebox.showinfo("نجح التنظيف", "تم تنظيف إعدادات GameLoop بنجاح!")

                except Exception as e:
                    self.update_status(f"خطأ في التنظيف: {str(e)}", self.colors['error'])
                    messagebox.showerror("خطأ", f"فشل في تنظيف الإعدادات: {str(e)}")

                finally:
                    self.progress.stop()

            threading.Thread(target=clean_thread, daemon=True).start()

    def system_check(self):
        """فحص شامل للنظام وإعدادات GameLoop"""
        def check_thread():
            self.progress.start()
            self.update_status("جاري فحص النظام...", self.colors['warning'])

            try:
                # إنشاء نافذة التقرير
                report_window = tk.Toplevel(self.root)
                report_window.title("تقرير فحص النظام")
                report_window.geometry("600x500")
                report_window.configure(bg=self.colors['bg'])

                # منطقة النص
                text_area = tk.Text(report_window, bg=self.colors['button_bg'],
                                  fg=self.colors['fg'], font=('Consolas', 10))
                text_area.pack(fill='both', expand=True, padx=10, pady=10)

                report = "🔍 تقرير فحص النظام - GameLoop Show Tabs Tool\n"
                report += "=" * 60 + "\n\n"

                # فحص نظام التشغيل
                report += f"🖥️ نظام التشغيل: {sys.platform}\n"
                report += f"📁 مجلد العمل: {os.getcwd()}\n"
                report += f"👤 صلاحيات المدير: {'نعم' if self.is_admin() else 'لا'}\n\n"

                # فحص GameLoop
                report += "🎮 فحص GameLoop:\n"
                gameloop_paths = [
                    os.path.expandvars(r"%PROGRAMFILES%\TxGameAssistant"),
                    os.path.expandvars(r"%PROGRAMFILES(X86)%\TxGameAssistant"),
                    os.path.expandvars(r"%LOCALAPPDATA%\TxGameAssistant")
                ]

                gameloop_found = False
                for path in gameloop_paths:
                    if os.path.exists(path):
                        report += f"   ✅ موجود في: {path}\n"
                        gameloop_found = True

                        # فحص الملفات الرئيسية
                        main_exe = os.path.join(path, "GameLoop.exe")
                        if os.path.exists(main_exe):
                            report += f"   ✅ الملف الرئيسي موجود\n"
                        else:
                            report += f"   ❌ الملف الرئيسي مفقود\n"

                if not gameloop_found:
                    report += "   ❌ GameLoop غير مثبت\n"

                report += "\n"

                # فحص العمليات
                report += "⚙️ العمليات النشطة:\n"
                gameloop_processes = []
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if 'gameloop' in proc.info['name'].lower() or 'txgame' in proc.info['name'].lower():
                            gameloop_processes.append(proc.info)
                    except:
                        pass

                if gameloop_processes:
                    for proc in gameloop_processes:
                        report += f"   🔄 {proc['name']} (PID: {proc['pid']})\n"
                else:
                    report += "   ⏹️ لا توجد عمليات GameLoop نشطة\n"

                report += "\n"

                # فحص الريجستري
                report += "📋 فحص إعدادات الريجستري:\n"
                for key_name, reg_path in self.gameloop_registry_keys.items():
                    try:
                        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_READ) as key:
                            report += f"   ✅ {key_name}: موجود\n"

                            # فحص إعدادات Show Tabs
                            try:
                                show_tabs = winreg.QueryValueEx(key, "ShowTabs")[0]
                                report += f"      📋 Show Tabs: {'مفعل' if show_tabs else 'غير مفعل'}\n"
                            except FileNotFoundError:
                                report += f"      📋 Show Tabs: غير محدد\n"

                    except FileNotFoundError:
                        report += f"   ❌ {key_name}: غير موجود\n"

                report += "\n"

                # فحص النسخ الاحتياطية
                report += "💾 النسخ الاحتياطية:\n"
                if os.path.exists(self.backup_folder):
                    backup_files = [f for f in os.listdir(self.backup_folder)
                                  if f.endswith('.reg')]
                    report += f"   📁 عدد النسخ الاحتياطية: {len(backup_files)}\n"
                    for backup in sorted(backup_files, reverse=True)[:3]:
                        report += f"      📄 {backup}\n"
                else:
                    report += "   ❌ مجلد النسخ الاحتياطية غير موجود\n"

                report += "\n" + "=" * 60 + "\n"
                report += f"⏰ تم إنشاء التقرير في: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

                text_area.insert('1.0', report)
                text_area.config(state='disabled')

                self.update_status("تم إنشاء تقرير فحص النظام", self.colors['success'])

            except Exception as e:
                self.update_status(f"خطأ في فحص النظام: {str(e)}", self.colors['error'])
                messagebox.showerror("خطأ", f"فشل في فحص النظام: {str(e)}")

            finally:
                self.progress.stop()

        threading.Thread(target=check_thread, daemon=True).start()

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        settings_file = "gameloop_settings.json"
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.hide_dots_var.set(settings.get('hide_dots', True))
                    self.advanced_optimization_var.set(settings.get('advanced_optimization', True))
                    self.auto_backup_var.set(settings.get('auto_backup', True))
                    self.auto_restart_var.set(settings.get('auto_restart', False))
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        settings_file = "gameloop_settings.json"
        try:
            settings = {
                'hide_dots': self.hide_dots_var.get(),
                'advanced_optimization': self.advanced_optimization_var.get(),
                'auto_backup': self.auto_backup_var.get(),
                'auto_restart': self.auto_restart_var.get()
            }
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.save_settings()
        self.root.destroy()

    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من نظام التشغيل
        if sys.platform != "win32":
            messagebox.showerror("خطأ", "هذه الأداة تعمل على Windows فقط")
            return

        # إنشاء وتشغيل التطبيق
        app = GameLoopShowTabsTool()
        app.run()

    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
