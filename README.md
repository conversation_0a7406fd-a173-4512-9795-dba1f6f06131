# 🎮 GameLoop Show Tabs - أداة احترافية محسنة

أداة احترافية متقدمة بلغة Python لتفعيل Show Tabs في محاكي Game Loop مع إخفاء النقاط البيضاء وميزات أمان متطورة.

## ✨ المميزات الجديدة والمحسنة

### 🔒 ميزات الأمان المتقدمة
- **صلاحيات المدير**: فحص وطلب صلاحيات المدير تلقائياً
- **نسخ احتياطي تلقائي**: إنشاء نسخة احتياطية قبل أي تعديل
- **استعادة آمنة**: استعادة الإعدادات من النسخ الاحتياطية
- **تشفير الإعدادات**: حماية الإعدادات الحساسة

### 🎯 تحسينات Show Tabs
- **إعدادات محسنة**: 12+ إعداد متقدم لتحسين Show Tabs
- **إخفاء النقاط البيضاء**: 10+ إعداد لإزالة جميع النقاط والتأثيرات
- **تحسين الأداء**: إلغاء الرسوم المتحركة والتأثيرات المزعجة
- **واجهة نظيفة**: وضع الواجهة المبسطة والنظيفة

### 🛠️ أدوات إضافية
- **فحص النظام**: تقرير شامل عن حالة GameLoop والنظام
- **تنظيف الإعدادات**: إزالة الإعدادات غير المرغوبة
- **إعادة تشغيل ذكي**: إعادة تشغيل GameLoop تلقائياً لتطبيق التغييرات
- **مراقبة العمليات**: فحص ومراقبة عمليات GameLoop النشطة

### 🎨 واجهة محسنة
- **تصميم احترافي**: واجهة مظلمة عصرية مع أيقونات
- **رسائل تفاعلية**: رسائل حالة ملونة وواضحة
- **شريط تقدم**: عرض تقدم العمليات بصرياً
- **نوافذ متعددة**: نوافذ منفصلة للأدوات المختلفة

## 🚀 كيفية الاستخدام

### المتطلبات
- نظام التشغيل: Windows 10/11
- Python 3.7 أو أحدث
- محاكي GameLoop مثبت

### التشغيل
1. تأكد من تثبيت Python على جهازك
2. قم بتشغيل الملف:
   ```bash
   python gameloop_show_tabs.py
   ```

### الخطوات
1. **فحص GameLoop**: اضغط على "فحص GameLoop" للتأكد من وجود المحاكي
2. **تفعيل Show Tabs**: اضغط على "تفعيل Show Tabs" لتفعيل الميزة
3. **إخفاء النقاط البيضاء**: تأكد من تفعيل الخيار لإخفاء النقاط البيضاء
4. **إعادة التشغيل**: أعد تشغيل GameLoop لتطبيق التغييرات

## 🎯 الوظائف الرئيسية

### فحص GameLoop
- يبحث عن GameLoop في المسارات الشائعة
- يعرض حالة التثبيت والمسار
- يفعل الأزرار عند العثور على المحاكي

### تفعيل Show Tabs
- يضيف إعدادات Show Tabs للريجستري
- يخفي النقاط البيضاء تلقائياً
- يحسن الأداء بإلغاء الرسوم المتحركة

### إلغاء التفعيل
- يزيل إعدادات Show Tabs
- يعيد الإعدادات الافتراضية
- يحافظ على استقرار المحاكي

## ⚙️ الخيارات المتقدمة

- **إخفاء النقاط البيضاء**: يخفي النقاط البيضاء المزعجة
- **التشغيل التلقائي**: تفعيل تلقائي عند بدء GameLoop (قريباً)

## 🔧 الإعدادات التقنية

الأداة تقوم بتعديل المفاتيح التالية في الريجستري:
```
HKEY_CURRENT_USER\SOFTWARE\TxGameAssistant\
- ShowTabs: 1 (تفعيل) / 0 (إلغاء)
- HideWhiteDots: 1 (إخفاء النقاط)
- TabsVisualEffects: 0 (إلغاء التأثيرات)
- TabsAnimation: 0 (إلغاء الرسوم المتحركة)
- TabsTransparency: 1 (تفعيل الشفافية)
```

## 🛡️ الأمان

- تعديل آمن للريجستري
- نسخ احتياطي تلقائي للإعدادات
- التحقق من صحة البيانات
- رسائل خطأ واضحة

## 📁 الملفات

- `gameloop_show_tabs.py`: الملف الرئيسي للأداة
- `gameloop_settings.json`: ملف حفظ الإعدادات (ينشأ تلقائياً)
- `README.md`: دليل الاستخدام

## 🐛 استكشاف الأخطاء

### المشاكل الشائعة:

1. **"GameLoop غير موجود"**
   - تأكد من تثبيت GameLoop بشكل صحيح
   - جرب إعادة تثبيت GameLoop

2. **"خطأ في الوصول للريجستري"**
   - شغل الأداة كمدير (Run as Administrator)
   - تأكد من صلاحيات المستخدم

3. **"لا تظهر التغييرات"**
   - أعد تشغيل GameLoop بالكامل
   - تأكد من إغلاق جميع نوافذ GameLoop

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من تشغيل الأداة كمدير
2. تحقق من وجود GameLoop
3. أعد تشغيل الجهاز إذا لزم الأمر

## 📝 ملاحظات

- الأداة تعمل على Windows فقط
- تحتاج صلاحيات المدير لتعديل الريجستري
- يُنصح بإنشاء نقطة استعادة قبل الاستخدام
- التغييرات تحتاج إعادة تشغيل GameLoop

---

**تم تطوير هذه الأداة لتحسين تجربة استخدام محاكي GameLoop وتوفير واجهة احترافية سهلة الاستخدام.**
