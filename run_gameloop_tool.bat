@echo off
title GameLoop Show Tabs Tool
echo.
echo ========================================
echo   GameLoop Show Tabs - اداة احترافية
echo ========================================
echo.
echo جاري تشغيل الاداة...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

REM تشغيل الاداة
python gameloop_show_tabs.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo حدث خطأ في تشغيل الاداة
    echo تأكد من وجود ملف gameloop_show_tabs.py
    pause
)
