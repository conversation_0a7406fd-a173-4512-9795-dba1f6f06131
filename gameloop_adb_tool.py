#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GameLoop ADB Show Tabs Tool - Enhanced Version
أداة احترافية للتحكم في GameLoop عبر ADB
مع تفعيل Show Tabs وإخفاء النقاط البيضاء
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
import json
import threading
import subprocess
import time
import zipfile
import urllib.request
from pathlib import Path

class GameLoopADBTool:
    def __init__(self):
        self.root = tk.Tk()
        self.adb_folder = "platform-tools"
        self.adb_path = os.path.join(self.adb_folder, "adb.exe")

        # إعدادات ADB للتحكم في GameLoop
        self.adb_commands = {
            'enable_show_taps': 'settings put system show_touches 1',
            'disable_show_taps': 'settings put system show_touches 0',
            'enable_pointer_location': 'settings put system pointer_location 1',
            'disable_pointer_location': 'settings put system pointer_location 0',
            'get_show_taps_status': 'settings get system show_touches',
            'get_pointer_status': 'settings get system pointer_location',
            # إعدادات إخفاء النقاط
            'hide_touch_dots': 'settings put system show_touches_alpha 0',
            'show_touch_dots': 'settings put system show_touches_alpha 255',
            'set_touch_size_small': 'settings put system touch_size_scale 0.3',
            'set_touch_size_normal': 'settings put system touch_size_scale 1.0',
            'get_touch_alpha': 'settings get system show_touches_alpha'
        }

        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.load_settings()

        # فحص وتحميل ADB تلقائياً
        self.root.after(1000, self.check_and_setup_adb)

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("GameLoop ADB Show Tabs - أداة احترافية")
        self.root.geometry("700x600")
        self.root.resizable(False, False)
        self.root.configure(bg='#1e1e1e')

        # وضع النافذة في المنتصف
        self.center_window()

    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"700x600+{x}+{y}")

    def setup_styles(self):
        """إعداد الأنماط الاحترافية"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        self.colors = {
            'bg': '#1e1e1e',
            'fg': '#ffffff',
            'accent': '#0078d4',
            'success': '#107c10',
            'warning': '#ff8c00',
            'error': '#d13438',
            'button_bg': '#2d2d30',
            'button_hover': '#3e3e42'
        }

        # تخصيص أنماط الأزرار
        self.style.configure('Custom.TButton',
                           background=self.colors['button_bg'],
                           foreground=self.colors['fg'],
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 10))

        self.style.map('Custom.TButton',
                      background=[('active', self.colors['button_hover']),
                                ('pressed', self.colors['accent'])])

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # العنوان الرئيسي
        title_label = tk.Label(main_frame,
                              text="🎮 GameLoop ADB Show Tabs Tool",
                              font=('Segoe UI', 18, 'bold'),
                              bg=self.colors['bg'],
                              fg=self.colors['accent'])
        title_label.pack(pady=(0, 10))

        # وصف الأداة
        desc_label = tk.Label(main_frame,
                             text="أداة احترافية للتحكم في GameLoop عبر ADB\nتفعيل Show Tabs وإخفاء النقاط البيضاء",
                             font=('Segoe UI', 10),
                             bg=self.colors['bg'],
                             fg=self.colors['fg'],
                             justify='center')
        desc_label.pack(pady=(0, 20))

        # إطار حالة ADB
        adb_frame = ttk.LabelFrame(main_frame, text="🔧 حالة ADB")
        adb_frame.pack(fill='x', pady=(0, 15))

        self.adb_status_label = tk.Label(adb_frame,
                                       text="🔍 فحص ADB...",
                                       font=('Segoe UI', 10),
                                       bg=self.colors['bg'],
                                       fg=self.colors['warning'])
        self.adb_status_label.pack(pady=10)

        # إطار حالة الاتصال
        connection_frame = ttk.LabelFrame(main_frame, text="📱 حالة الاتصال")
        connection_frame.pack(fill='x', pady=(0, 15))

        self.connection_status_label = tk.Label(connection_frame,
                                              text="📱 غير متصل",
                                              font=('Segoe UI', 10),
                                              bg=self.colors['bg'],
                                              fg=self.colors['error'])
        self.connection_status_label.pack(pady=5)

        self.device_info_label = tk.Label(connection_frame,
                                        text="",
                                        font=('Segoe UI', 9),
                                        bg=self.colors['bg'],
                                        fg=self.colors['fg'])
        self.device_info_label.pack(pady=5)

        # أزرار الاتصال
        connection_buttons_frame = ttk.Frame(connection_frame)
        connection_buttons_frame.pack(fill='x', padx=10, pady=10)

        self.connect_button = ttk.Button(connection_buttons_frame,
                                       text="🔌 اتصال بـ GameLoop",
                                       style='Custom.TButton',
                                       command=self.connect_to_gameloop)
        self.connect_button.pack(side='left', padx=(0, 5), fill='x', expand=True)

        self.refresh_button = ttk.Button(connection_buttons_frame,
                                       text="🔄 تحديث",
                                       style='Custom.TButton',
                                       command=self.refresh_connection)
        self.refresh_button.pack(side='left', padx=5, fill='x', expand=True)

        # إطار التحكم الرئيسي
        control_frame = ttk.LabelFrame(main_frame, text="🎯 التحكم في Show Tabs")
        control_frame.pack(fill='x', pady=(0, 15))

        # حالة Show Tabs
        self.show_tabs_status_label = tk.Label(control_frame,
                                             text="📋 Show Tabs: غير معروف",
                                             font=('Segoe UI', 10),
                                             bg=self.colors['bg'],
                                             fg=self.colors['fg'])
        self.show_tabs_status_label.pack(pady=10)

        # أزرار التحكم
        control_buttons_frame = ttk.Frame(control_frame)
        control_buttons_frame.pack(fill='x', padx=10, pady=10)

        self.enable_tabs_button = ttk.Button(control_buttons_frame,
                                           text="✅ تفعيل Show Tabs",
                                           style='Custom.TButton',
                                           command=self.enable_show_tabs,
                                           state='disabled')
        self.enable_tabs_button.pack(side='left', padx=(0, 5), fill='x', expand=True)

        self.disable_tabs_button = ttk.Button(control_buttons_frame,
                                            text="❌ إلغاء Show Tabs",
                                            style='Custom.TButton',
                                            command=self.disable_show_tabs,
                                            state='disabled')
        self.disable_tabs_button.pack(side='left', padx=5, fill='x', expand=True)

        # إطار الخيارات المتقدمة
        options_frame = ttk.LabelFrame(main_frame, text="⚙️ خيارات متقدمة")
        options_frame.pack(fill='x', pady=(0, 15))

        # إطار فرعي للخيارات في صفوف
        options_grid_frame = ttk.Frame(options_frame)
        options_grid_frame.pack(fill='x', padx=10, pady=10)

        # الصف الأول: Pointer Location
        self.pointer_location_var = tk.BooleanVar()
        pointer_check = tk.Checkbutton(options_grid_frame,
                                     text="🎯 إظهار موقع المؤشر",
                                     variable=self.pointer_location_var,
                                     bg=self.colors['bg'],
                                     fg=self.colors['fg'],
                                     selectcolor=self.colors['button_bg'],
                                     activebackground=self.colors['bg'],
                                     activeforeground=self.colors['fg'],
                                     font=('Segoe UI', 10),
                                     command=self.toggle_pointer_location)
        pointer_check.grid(row=0, column=0, sticky='w', padx=5, pady=2)

        # الصف الثاني: إخفاء النقاط
        self.hide_dots_var = tk.BooleanVar()
        self.hide_dots_check = tk.Checkbutton(options_grid_frame,
                                            text="🔘 إخفاء النقاط عند اللمس",
                                            variable=self.hide_dots_var,
                                            bg=self.colors['bg'],
                                            fg=self.colors['fg'],
                                            selectcolor=self.colors['button_bg'],
                                            activebackground=self.colors['bg'],
                                            activeforeground=self.colors['fg'],
                                            font=('Segoe UI', 10),
                                            command=self.toggle_hide_dots)
        self.hide_dots_check.grid(row=1, column=0, sticky='w', padx=5, pady=2)

        # الصف الثالث: تصغير النقاط
        self.small_dots_var = tk.BooleanVar()
        self.small_dots_check = tk.Checkbutton(options_grid_frame,
                                             text="🔸 تصغير حجم النقاط",
                                             variable=self.small_dots_var,
                                             bg=self.colors['bg'],
                                             fg=self.colors['fg'],
                                             selectcolor=self.colors['button_bg'],
                                             activebackground=self.colors['bg'],
                                             activeforeground=self.colors['fg'],
                                             font=('Segoe UI', 10),
                                             command=self.toggle_small_dots)
        self.small_dots_check.grid(row=2, column=0, sticky='w', padx=5, pady=2)

        # شريط التقدم
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill='x', pady=10)

        # شريط الحالة
        self.status_bar = tk.Label(main_frame,
                                 text="جاهز للاستخدام - يرجى تحميل ADB أولاً",
                                 font=('Segoe UI', 9),
                                 bg=self.colors['button_bg'],
                                 fg=self.colors['fg'],
                                 relief='sunken',
                                 anchor='w')
        self.status_bar.pack(fill='x', side='bottom')

    def update_status(self, message, color=None):
        """تحديث شريط الحالة"""
        if color is None:
            color = self.colors['fg']
        self.status_bar.config(text=message, fg=color)
        self.root.update()

    def check_and_setup_adb(self):
        """فحص وإعداد ADB"""
        def setup_thread():
            self.progress.start()
            self.update_status("جاري فحص ADB...", self.colors['warning'])

            if os.path.exists(self.adb_path):
                self.adb_status_label.config(
                    text="✅ ADB موجود وجاهز",
                    fg=self.colors['success']
                )
                self.update_status("ADB جاهز - يمكنك الاتصال بـ GameLoop", self.colors['success'])
                self.connect_button.config(state='normal')
            else:
                self.download_adb()

            self.progress.stop()

        threading.Thread(target=setup_thread, daemon=True).start()

    def download_adb(self):
        """تحميل ADB platform-tools"""
        try:
            self.update_status("جاري تحميل ADB platform-tools...", self.colors['warning'])

            # رابط تحميل platform-tools
            adb_url = "https://dl.google.com/android/repository/platform-tools-latest-windows.zip"
            zip_file = "platform-tools.zip"

            # تحميل الملف
            urllib.request.urlretrieve(adb_url, zip_file)

            self.update_status("جاري استخراج الملفات...", self.colors['warning'])

            # استخراج الملفات
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall('.')

            # حذف ملف الـ zip
            os.remove(zip_file)

            if os.path.exists(self.adb_path):
                self.adb_status_label.config(
                    text="✅ تم تحميل ADB بنجاح",
                    fg=self.colors['success']
                )
                self.update_status("ADB جاهز - يمكنك الاتصال بـ GameLoop", self.colors['success'])
                self.connect_button.config(state='normal')
            else:
                raise Exception("فشل في استخراج ADB")

        except Exception as e:
            self.adb_status_label.config(
                text=f"❌ فشل في تحميل ADB: {str(e)}",
                fg=self.colors['error']
            )
            self.update_status(f"خطأ في تحميل ADB: {str(e)}", self.colors['error'])
            messagebox.showerror("خطأ", f"فشل في تحميل ADB:\n{str(e)}")

    def run_adb_command(self, command):
        """تشغيل أمر ADB"""
        try:
            full_command = f'"{self.adb_path}" {command}'
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=10)
            return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
        except subprocess.TimeoutExpired:
            return False, "", "انتهت مهلة الأمر"
        except Exception as e:
            return False, "", str(e)

    def connect_to_gameloop(self):
        """الاتصال بـ GameLoop"""
        def connect_thread():
            self.progress.start()
            self.update_status("جاري البحث عن GameLoop...", self.colors['warning'])

            try:
                # البحث عن أجهزة متصلة
                success, output, error = self.run_adb_command("devices")

                if not success:
                    self.update_status("فشل في تشغيل ADB", self.colors['error'])
                    messagebox.showerror("خطأ", f"فشل في تشغيل ADB:\n{error}")
                    return

                # تحليل قائمة الأجهزة
                devices = []
                lines = output.split('\n')[1:]  # تجاهل السطر الأول
                for line in lines:
                    if line.strip() and '\t' in line:
                        device_id, status = line.split('\t')
                        if status == 'device':
                            devices.append(device_id)

                if not devices:
                    # محاولة الاتصال بـ GameLoop على المنافذ الشائعة
                    gameloop_ports = ['21503', '21513', '21523', '21533']

                    for port in gameloop_ports:
                        self.update_status(f"محاولة الاتصال بالمنفذ {port}...", self.colors['warning'])
                        success, _, _ = self.run_adb_command(f"connect 127.0.0.1:{port}")

                        if success:
                            time.sleep(2)  # انتظار قصير
                            success, output, _ = self.run_adb_command("devices")
                            if success and '127.0.0.1:' + port in output:
                                devices.append(f"127.0.0.1:{port}")
                                break

                if devices:
                    device_id = devices[0]  # استخدام أول جهاز
                    self.current_device = device_id

                    # الحصول على معلومات الجهاز
                    success, model, _ = self.run_adb_command(f"-s {device_id} shell getprop ro.product.model")
                    success2, android_version, _ = self.run_adb_command(f"-s {device_id} shell getprop ro.build.version.release")

                    device_info = f"الجهاز: {model if success else 'غير معروف'}"
                    if success2:
                        device_info += f" | Android {android_version}"

                    self.connection_status_label.config(
                        text=f"✅ متصل: {device_id}",
                        fg=self.colors['success']
                    )
                    self.device_info_label.config(text=device_info)

                    # تفعيل أزرار التحكم
                    self.enable_tabs_button.config(state='normal')
                    self.disable_tabs_button.config(state='normal')

                    self.update_status("تم الاتصال بنجاح - جاري فحص حالة Show Tabs...", self.colors['success'])

                    # فحص حالة Show Tabs الحالية
                    self.check_show_tabs_status()

                else:
                    self.connection_status_label.config(
                        text="❌ لم يتم العثور على GameLoop",
                        fg=self.colors['error']
                    )
                    self.device_info_label.config(text="تأكد من تشغيل GameLoop وتفعيل USB Debugging")
                    self.update_status("لم يتم العثور على GameLoop", self.colors['error'])

                    messagebox.showwarning("لم يتم العثور على GameLoop",
                                         "تأكد من:\n"
                                         "1. تشغيل GameLoop\n"
                                         "2. تفعيل Developer Options\n"
                                         "3. تفعيل USB Debugging\n"
                                         "4. السماح لـ ADB بالاتصال")

            except Exception as e:
                self.update_status(f"خطأ في الاتصال: {str(e)}", self.colors['error'])
                messagebox.showerror("خطأ", f"فشل في الاتصال:\n{str(e)}")

            finally:
                self.progress.stop()

        threading.Thread(target=connect_thread, daemon=True).start()

    def refresh_connection(self):
        """تحديث الاتصال"""
        self.connect_to_gameloop()

    def check_show_tabs_status(self):
        """فحص حالة Show Tabs الحالية وإظهار/إخفاء الخيارات الإضافية"""
        if not hasattr(self, 'current_device'):
            return

        try:
            # فحص Show Touches
            success, output, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['get_show_taps_status']}")

            if success:
                show_tabs_enabled = output.strip() == '1'
                status_text = "مفعل ✅" if show_tabs_enabled else "غير مفعل ❌"
                status_color = self.colors['success'] if show_tabs_enabled else self.colors['error']

                self.show_tabs_status_label.config(
                    text=f"📋 Show Tabs: {status_text}",
                    fg=status_color
                )

                # إظهار/إخفاء خيارات إخفاء النقاط حسب حالة Show Tabs
                if show_tabs_enabled:
                    # إظهار خيارات إخفاء النقاط
                    self.hide_dots_check.pack(anchor='w', padx=10, pady=5)
                    self.small_dots_check.pack(anchor='w', padx=10, pady=5)

                    # فحص حالة إخفاء النقاط الحالية
                    self.check_dots_settings()
                else:
                    # إخفاء خيارات إخفاء النقاط
                    self.hide_dots_check.pack_forget()
                    self.small_dots_check.pack_forget()

            else:
                self.show_tabs_status_label.config(
                    text="📋 Show Tabs: خطأ في القراءة",
                    fg=self.colors['warning']
                )
                # إخفاء الخيارات عند الخطأ
                self.hide_dots_check.pack_forget()
                self.small_dots_check.pack_forget()

        except Exception as e:
            print(f"خطأ في فحص حالة Show Tabs: {e}")

    def check_dots_settings(self):
        """فحص إعدادات النقاط الحالية"""
        try:
            # فحص شفافية النقاط
            success, alpha_output, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['get_touch_alpha']}")

            if success and alpha_output.strip():
                alpha_value = alpha_output.strip()
                # إذا كانت الشفافية 0 أو قريبة من 0، فالنقاط مخفية
                is_hidden = alpha_value == '0' or alpha_value == 'null'
                self.hide_dots_var.set(is_hidden)

        except Exception as e:
            print(f"خطأ في فحص إعدادات النقاط: {e}")

    def enable_show_tabs(self):
        """تفعيل Show Tabs"""
        def enable_thread():
            self.progress.start()
            self.update_status("جاري تفعيل Show Tabs...", self.colors['warning'])

            try:
                # تفعيل Show Touches
                success, output, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['enable_show_taps']}")

                if success:
                    self.update_status("تم تفعيل Show Tabs بنجاح!", self.colors['success'])
                    self.check_show_tabs_status()

                    messagebox.showinfo("نجح التفعيل",
                                      "✅ تم تفعيل Show Tabs بنجاح!\n"
                                      "الآن ستظهر النقاط عند اللمس في GameLoop")
                else:
                    self.update_status(f"فشل في تفعيل Show Tabs: {error}", self.colors['error'])
                    messagebox.showerror("خطأ", f"فشل في تفعيل Show Tabs:\n{error}")

            except Exception as e:
                self.update_status(f"خطأ: {str(e)}", self.colors['error'])
                messagebox.showerror("خطأ", f"حدث خطأ:\n{str(e)}")

            finally:
                self.progress.stop()

        threading.Thread(target=enable_thread, daemon=True).start()

    def disable_show_tabs(self):
        """إلغاء تفعيل Show Tabs"""
        def disable_thread():
            self.progress.start()
            self.update_status("جاري إلغاء تفعيل Show Tabs...", self.colors['warning'])

            try:
                # إلغاء تفعيل Show Touches
                success, output, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['disable_show_taps']}")

                if success:
                    self.update_status("تم إلغاء تفعيل Show Tabs بنجاح!", self.colors['success'])
                    self.check_show_tabs_status()

                    messagebox.showinfo("تم الإلغاء",
                                      "❌ تم إلغاء تفعيل Show Tabs بنجاح!\n"
                                      "لن تظهر النقاط عند اللمس في GameLoop")
                else:
                    self.update_status(f"فشل في إلغاء التفعيل: {error}", self.colors['error'])
                    messagebox.showerror("خطأ", f"فشل في إلغاء التفعيل:\n{error}")

            except Exception as e:
                self.update_status(f"خطأ: {str(e)}", self.colors['error'])
                messagebox.showerror("خطأ", f"حدث خطأ:\n{str(e)}")

            finally:
                self.progress.stop()

        threading.Thread(target=disable_thread, daemon=True).start()

    def toggle_hide_dots(self):
        """تبديل حالة إخفاء النقاط"""
        if not hasattr(self, 'current_device'):
            messagebox.showwarning("تحذير", "يرجى الاتصال بـ GameLoop أولاً")
            self.hide_dots_var.set(False)
            return

        # التحقق من أن Show Tabs مفعل
        success, output, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['get_show_taps_status']}")
        if not success or output.strip() != '1':
            messagebox.showwarning("تحذير", "يجب تفعيل Show Tabs أولاً لاستخدام هذا الخيار")
            self.hide_dots_var.set(False)
            return

        def toggle_thread():
            self.progress.start()

            try:
                if self.hide_dots_var.get():
                    # إخفاء النقاط (جعلها شفافة)
                    success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['hide_touch_dots']}")
                    action = "إخفاء النقاط"
                    success_msg = "تم إخفاء النقاط بنجاح! 🔘"
                else:
                    # إظهار النقاط (إعادة الشفافية العادية)
                    success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['show_touch_dots']}")
                    action = "إظهار النقاط"
                    success_msg = "تم إظهار النقاط بنجاح! ⚪"

                if success:
                    self.update_status(success_msg, self.colors['success'])
                    messagebox.showinfo("نجح التغيير", success_msg)
                else:
                    self.update_status(f"فشل في {action}: {error}", self.colors['error'])
                    # إعادة تعيين الحالة
                    self.hide_dots_var.set(not self.hide_dots_var.get())

            except Exception as e:
                self.update_status(f"خطأ: {str(e)}", self.colors['error'])
                self.hide_dots_var.set(not self.hide_dots_var.get())

            finally:
                self.progress.stop()

        threading.Thread(target=toggle_thread, daemon=True).start()

    def toggle_small_dots(self):
        """تبديل حالة تصغير النقاط"""
        if not hasattr(self, 'current_device'):
            messagebox.showwarning("تحذير", "يرجى الاتصال بـ GameLoop أولاً")
            self.small_dots_var.set(False)
            return

        # التحقق من أن Show Tabs مفعل
        success, output, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['get_show_taps_status']}")
        if not success or output.strip() != '1':
            messagebox.showwarning("تحذير", "يجب تفعيل Show Tabs أولاً لاستخدام هذا الخيار")
            self.small_dots_var.set(False)
            return

        def toggle_thread():
            self.progress.start()

            try:
                if self.small_dots_var.get():
                    # تصغير النقاط
                    success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['set_touch_size_small']}")
                    action = "تصغير النقاط"
                    success_msg = "تم تصغير النقاط بنجاح! 🔸"
                else:
                    # إعادة الحجم العادي
                    success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['set_touch_size_normal']}")
                    action = "إعادة الحجم العادي"
                    success_msg = "تم إعادة الحجم العادي للنقاط! ⚪"

                if success:
                    self.update_status(success_msg, self.colors['success'])
                    messagebox.showinfo("نجح التغيير", success_msg)
                else:
                    self.update_status(f"فشل في {action}: {error}", self.colors['error'])
                    # إعادة تعيين الحالة
                    self.small_dots_var.set(not self.small_dots_var.get())

            except Exception as e:
                self.update_status(f"خطأ: {str(e)}", self.colors['error'])
                self.small_dots_var.set(not self.small_dots_var.get())

            finally:
                self.progress.stop()

        threading.Thread(target=toggle_thread, daemon=True).start()

    def toggle_pointer_location(self):
        """تبديل حالة Pointer Location"""
        if not hasattr(self, 'current_device'):
            messagebox.showwarning("تحذير", "يرجى الاتصال بـ GameLoop أولاً")
            self.pointer_location_var.set(False)
            return

        def toggle_thread():
            self.progress.start()

            try:
                if self.pointer_location_var.get():
                    # تفعيل Pointer Location
                    success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['enable_pointer_location']}")
                    action = "تفعيل"
                else:
                    # إلغاء تفعيل Pointer Location
                    success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['disable_pointer_location']}")
                    action = "إلغاء تفعيل"

                if success:
                    self.update_status(f"تم {action} Pointer Location بنجاح!", self.colors['success'])
                else:
                    self.update_status(f"فشل في {action} Pointer Location: {error}", self.colors['error'])
                    # إعادة تعيين الحالة
                    self.pointer_location_var.set(not self.pointer_location_var.get())

            except Exception as e:
                self.update_status(f"خطأ: {str(e)}", self.colors['error'])
                self.pointer_location_var.set(not self.pointer_location_var.get())

            finally:
                self.progress.stop()

        threading.Thread(target=toggle_thread, daemon=True).start()

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        settings_file = "gameloop_adb_settings.json"
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.pointer_location_var.set(settings.get('pointer_location', False))
                    self.hide_dots_var.set(settings.get('hide_dots', False))
                    self.small_dots_var.set(settings.get('small_dots', False))
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        settings_file = "gameloop_adb_settings.json"
        try:
            settings = {
                'pointer_location': self.pointer_location_var.get(),
                'hide_dots': self.hide_dots_var.get(),
                'small_dots': self.small_dots_var.get()
            }
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.save_settings()
        self.root.destroy()

    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = GameLoopADBTool()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
