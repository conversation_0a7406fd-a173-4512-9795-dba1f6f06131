# GameLoop Show Tabs Tool - المتطلبات
# جميع المكتبات المطلوبة مدمجة في Python الأساسي

# المكتبات المستخدمة (مدمجة):
# - tkinter (واجهة رسومية)
# - winreg (تعديل الريجستري)
# - os (عمليات النظام)
# - sys (معلومات النظام)
# - json (حفظ الإعدادات)
# - threading (المعالجة المتوازية)
# - pathlib (التعامل مع المسارات)
# - subprocess (تشغيل العمليات)

# لا توجد مكتبات خارجية مطلوبة
# الأداة تعمل مع Python الأساسي فقط
