# 🎮 GameLoop ADB Show Tabs Tool - أداة احترافية

أداة احترافية متطورة تستخدم ADB للتحكم الحقيقي في GameLoop وتفعيل Show Tabs بشكل فعال.

## ✨ المميزات الجديدة

### 🔧 تقنية ADB المتقدمة
- **تحميل تلقائي لـ ADB**: تحميل platform-tools تلقائياً
- **اتصال ذكي**: البحث التلقائي عن GameLoop على المنافذ المختلفة
- **تحكم حقيقي**: استخدام أوامر Android الأصلية
- **مراقبة الحالة**: فحص مستمر لحالة الاتصال والإعدادات

### 🎯 وظائف Show Tabs الحقيقية
- **تفعيل Show Touches**: `settings put system show_touches 1`
- **إلغاء Show Touches**: `settings put system show_touches 0`
- **Pointer Location**: إظهار موقع المؤشر بدقة
- **فحص الحالة**: معرفة الحالة الحالية للإعدادات

### 🛡️ الأمان والاستقرار
- **اتصال آمن**: استخدام بروتوكولات ADB الآمنة
- **معالجة الأخطاء**: معالجة شاملة للأخطاء المحتملة
- **حفظ الإعدادات**: حفظ تلقائي للتفضيلات
- **واجهة مستقرة**: عدم تجمد الواجهة أثناء العمليات

## 🚀 كيفية الاستخدام

### المتطلبات
- نظام التشغيل: Windows 10/11
- Python 3.7 أو أحدث
- محاكي GameLoop مثبت ويعمل
- اتصال بالإنترنت (لتحميل ADB في المرة الأولى)

### خطوات التشغيل

#### 1. تشغيل الأداة
```bash
python gameloop_adb_tool.py
```
أو استخدم الملف المساعد:
```bash
run_gameloop_adb_tool.bat
```

#### 2. إعداد GameLoop
1. شغل GameLoop
2. اذهب إلى Settings → Advanced
3. فعل "Developer Options"
4. فعل "USB Debugging"

#### 3. الاتصال
1. اضغط "اتصال بـ GameLoop"
2. انتظر حتى يتم العثور على GameLoop
3. اسمح للاتصال إذا ظهرت رسالة تأكيد

#### 4. التحكم
- **تفعيل Show Tabs**: اضغط "تفعيل Show Tabs"
- **إلغاء Show Tabs**: اضغط "إلغاء Show Tabs"
- **Pointer Location**: فعل/ألغ الخيار حسب الحاجة

## 🔧 الإعدادات التقنية

### أوامر ADB المستخدمة
```bash
# تفعيل Show Touches
adb shell settings put system show_touches 1

# إلغاء Show Touches
adb shell settings put system show_touches 0

# تفعيل Pointer Location
adb shell settings put system pointer_location 1

# فحص الحالة
adb shell settings get system show_touches
```

### منافذ GameLoop الافتراضية
- 21503 (الافتراضي)
- 21513
- 21523
- 21533

## 📁 هيكل الملفات

```
📁 المجلد الرئيسي/
├── 📄 gameloop_adb_tool.py          # الأداة الرئيسية
├── 📄 run_gameloop_adb_tool.bat     # ملف التشغيل المساعد
├── 📄 README_ADB.md                 # دليل الاستخدام
├── 📁 platform-tools/               # مجلد ADB (ينشأ تلقائياً)
│   ├── 📄 adb.exe
│   ├── 📄 fastboot.exe
│   └── 📄 ...
└── 📄 gameloop_adb_settings.json    # ملف الإعدادات (ينشأ تلقائياً)
```

## 🐛 استكشاف الأخطاء

### المشاكل الشائعة:

#### "لم يتم العثور على GameLoop"
**الحلول:**
1. تأكد من تشغيل GameLoop
2. فعل Developer Options في GameLoop
3. فعل USB Debugging
4. أعد تشغيل GameLoop والأداة

#### "فشل في تحميل ADB"
**الحلول:**
1. تحقق من اتصال الإنترنت
2. شغل الأداة كمدير (Run as Administrator)
3. تأكد من عدم حجب Antivirus للتحميل

#### "فشل في تفعيل Show Tabs"
**الحلول:**
1. تأكد من الاتصال بـ GameLoop
2. أعد تشغيل GameLoop
3. جرب الاتصال مرة أخرى

### رسائل الحالة:
- 🔍 **فحص ADB**: الأداة تتحقق من وجود ADB
- 📱 **غير متصل**: لم يتم الاتصال بـ GameLoop بعد
- ✅ **متصل**: تم الاتصال بنجاح
- 📋 **Show Tabs: مفعل**: الميزة تعمل
- ❌ **Show Tabs: غير مفعل**: الميزة متوقفة

## 🎯 المميزات المتقدمة

### تحميل ADB التلقائي
- تحميل أحدث إصدار من platform-tools
- استخراج تلقائي للملفات
- فحص تلقائي للتحديثات

### الاتصال الذكي
- البحث في المنافذ المختلفة
- إعادة المحاولة التلقائية
- معلومات مفصلة عن الجهاز

### واجهة احترافية
- تصميم مظلم عصري
- رسائل حالة ملونة
- شريط تقدم للعمليات
- حفظ تلقائي للإعدادات

## 📈 الأداء

- **سرعة الاتصال**: أقل من 5 ثوانٍ
- **استهلاك الذاكرة**: أقل من 50 MB
- **حجم التحميل**: حوالي 10 MB (platform-tools)
- **التوافق**: جميع إصدارات GameLoop

## 🔄 التحديثات المستقبلية

- [ ] دعم أجهزة متعددة
- [ ] إعدادات متقدمة إضافية
- [ ] واجهة ويب اختيارية
- [ ] دعم macOS و Linux

---

**هذه الأداة تستخدم تقنية ADB الحقيقية للتحكم في GameLoop بشكل فعال ومضمون!** 🚀✨
