#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GameLoop ADB Show Tabs Pro - Ultimate Professional Edition
أداة احترافية فاخرة للتحكم في GameLoop عبر ADB
تصميم أسود وذهبي عصري مع دقة عالية
"""

import os
import subprocess
import threading
import urllib.request
import zipfile
import json
import time
import tkinter as tk
from tkinter import ttk, messagebox

class GameLoopADBPro:
    def __init__(self):
        super().__init__()
        self.adb_folder = "platform-tools"
        self.adb_path = os.path.join(self.adb_folder, "adb.exe")
        self.current_device = None

        # إعدادات ADB المحسنة
        self.adb_commands = {
            'enable_show_taps': 'settings put system show_touches 1',
            'disable_show_taps': 'settings put system show_touches 0',
            'get_show_taps_status': 'settings get system show_touches',

            # طرق إخفاء النقاط البيضاء - 10 طرق مختلفة
            'hide_method_1': 'settings put system show_touches 1 && settings put system show_touches 0 && settings put system show_touches 1 && settings put system show_touches 0',
            'hide_method_2': 'settings put system show_touches 1 && settings put secure show_touches 0',
            'hide_method_3': 'settings put system show_touches 1 && settings put global show_touches 0',
            'hide_method_4': 'settings put system show_touches 1 && setprop debug.show_touches false',
            'hide_method_5': 'settings put system show_touches 1 && settings put system show_touches 0 && sleep 0.1 && settings put system show_touches 1',
            'hide_method_6': 'settings put system show_touches 1 && settings delete system show_touches && settings put system show_touches 1',
            'hide_method_7': 'settings put system show_touches 1 && settings put system show_touches null',
            'hide_method_8': 'settings put system show_touches 1 && settings put system show_touches_alpha 0',
            'hide_method_9': 'settings put system show_touches 1 && settings put system touch_size_scale 0.01',
            'hide_method_10': 'settings put system show_touches 1 && settings put system pointer_size 0.1',

            # طريقة خاصة بـ GameLoop
            'gameloop_ultimate_hide': 'settings put system show_touches 1 && settings put system show_touches 0 && settings put system show_touches 1 && settings put global show_touches 0 && settings put secure show_touches 0 && settings put system show_touches_alpha 0',

            # إعادة النقاط
            'restore_touches': 'settings put system show_touches 1 && settings delete global show_touches && settings delete secure show_touches && settings put system show_touches_alpha 255',
        }

        self.init_ui()
        self.apply_luxury_style()
        self.check_adb_status()

    def init_ui(self):
        """إنشاء الواجهة الاحترافية"""
        self.setWindowTitle("🎮 GameLoop ADB Show Tabs Pro - Ultimate Edition")
        self.setGeometry(100, 100, 900, 800)
        self.setMinimumSize(850, 750)

        # النافذة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)

        # العنوان الفاخر
        self.create_header(main_layout)

        # قسم حالة ADB
        self.create_adb_section(main_layout)

        # قسم الاتصال
        self.create_connection_section(main_layout)

        # قسم التحكم في Show Tabs
        self.create_show_tabs_section(main_layout)

        # قسم الخيارات المتقدمة
        self.create_advanced_options(main_layout)

        # شريط الحالة الفاخر
        self.create_status_bar()

    def create_header(self, layout):
        """إنشاء العنوان الفاخر"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)

        # العنوان الرئيسي
        title = QLabel("🎮 GameLoop ADB Show Tabs Pro")
        title.setObjectName("mainTitle")
        title.setAlignment(Qt.AlignCenter)

        # العنوان الفرعي
        subtitle = QLabel("✨ Ultimate Professional Edition ✨")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignCenter)

        # الوصف
        description = QLabel("🚀 تحكم كامل في GameLoop • 🔘 إخفاء النقاط البيضاء بـ 10 طرق\n⚡ أداء فائق • 🎯 تصميم فاخر أسود وذهبي")
        description.setObjectName("description")
        description.setAlignment(Qt.AlignCenter)

        header_layout.addWidget(title)
        header_layout.addWidget(subtitle)
        header_layout.addWidget(description)

        layout.addWidget(header_frame)

    def create_adb_section(self, layout):
        """قسم حالة ADB"""
        adb_group = QGroupBox("🔧 حالة ADB")
        adb_group.setObjectName("luxuryGroup")
        adb_layout = QVBoxLayout(adb_group)

        self.adb_status = QLabel("🔍 جاري فحص ADB...")
        self.adb_status.setObjectName("statusLabel")
        self.adb_status.setAlignment(Qt.AlignCenter)

        adb_layout.addWidget(self.adb_status)
        layout.addWidget(adb_group)

    def create_connection_section(self, layout):
        """قسم الاتصال"""
        conn_group = QGroupBox("📱 الاتصال بـ GameLoop")
        conn_group.setObjectName("luxuryGroup")
        conn_layout = QVBoxLayout(conn_group)

        # حالة الاتصال
        self.connection_status = QLabel("📱 غير متصل")
        self.connection_status.setObjectName("connectionStatus")
        self.connection_status.setAlignment(Qt.AlignCenter)

        # معلومات الجهاز
        self.device_info = QLabel("")
        self.device_info.setObjectName("deviceInfo")
        self.device_info.setAlignment(Qt.AlignCenter)

        # أزرار الاتصال
        buttons_layout = QHBoxLayout()

        self.connect_btn = QPushButton("🔌 اتصال بـ GameLoop")
        self.connect_btn.setObjectName("primaryButton")
        self.connect_btn.clicked.connect(self.connect_to_gameloop)

        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setObjectName("secondaryButton")
        self.refresh_btn.clicked.connect(self.connect_to_gameloop)

        buttons_layout.addWidget(self.connect_btn)
        buttons_layout.addWidget(self.refresh_btn)

        conn_layout.addWidget(self.connection_status)
        conn_layout.addWidget(self.device_info)
        conn_layout.addLayout(buttons_layout)

        layout.addWidget(conn_group)

    def create_show_tabs_section(self, layout):
        """قسم التحكم في Show Tabs"""
        tabs_group = QGroupBox("🎯 التحكم في Show Tabs")
        tabs_group.setObjectName("luxuryGroup")
        tabs_layout = QVBoxLayout(tabs_group)

        # حالة Show Tabs
        self.show_tabs_status = QLabel("📋 Show Tabs: غير معروف")
        self.show_tabs_status.setObjectName("showTabsStatus")
        self.show_tabs_status.setAlignment(Qt.AlignCenter)

        # أزرار التحكم
        tabs_buttons_layout = QHBoxLayout()

        self.enable_tabs_btn = QPushButton("✅ تفعيل Show Tabs")
        self.enable_tabs_btn.setObjectName("successButton")
        self.enable_tabs_btn.clicked.connect(self.enable_show_tabs)
        self.enable_tabs_btn.setEnabled(False)

        self.disable_tabs_btn = QPushButton("❌ إلغاء Show Tabs")
        self.disable_tabs_btn.setObjectName("dangerButton")
        self.disable_tabs_btn.clicked.connect(self.disable_show_tabs)
        self.disable_tabs_btn.setEnabled(False)

        tabs_buttons_layout.addWidget(self.enable_tabs_btn)
        tabs_buttons_layout.addWidget(self.disable_tabs_btn)

        tabs_layout.addWidget(self.show_tabs_status)
        tabs_layout.addLayout(tabs_buttons_layout)

        layout.addWidget(tabs_group)

    def create_advanced_options(self, layout):
        """الخيارات المتقدمة"""
        options_group = QGroupBox("⚙️ خيارات متقدمة فاخرة")
        options_group.setObjectName("luxuryGroup")
        options_layout = QVBoxLayout(options_group)

        # خيار إخفاء النقاط
        self.hide_dots_check = QCheckBox("🔘 إخفاء النقاط البيضاء نهائياً (10 طرق احترافية)")
        self.hide_dots_check.setObjectName("luxuryCheckbox")
        self.hide_dots_check.stateChanged.connect(self.toggle_hide_dots)

        options_layout.addWidget(self.hide_dots_check)
        layout.addWidget(options_group)

    def create_status_bar(self):
        """شريط الحالة الفاخر"""
        self.status_bar = self.statusBar()
        self.status_bar.setObjectName("luxuryStatusBar")
        self.status_bar.showMessage("🚀 جاهز للاستخدام - أداة احترافية فاخرة")

    def apply_luxury_style(self):
        """تطبيق التصميم الفاخر الأسود والذهبي"""
        style = """
        /* النافذة الرئيسية */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0a0a0a, stop:1 #1a1a1a);
            color: #FFD700;
        }

        /* العنوان الرئيسي */
        #mainTitle {
            font-family: 'Segoe UI Black';
            font-size: 28px;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(255, 215, 0, 0.3);
            margin: 10px;
        }

        /* العنوان الفرعي */
        #subtitle {
            font-family: 'Segoe UI';
            font-size: 16px;
            font-weight: bold;
            color: #FFA500;
            margin: 5px;
        }

        /* الوصف */
        #description {
            font-family: 'Segoe UI';
            font-size: 12px;
            color: #CCCCCC;
            margin: 10px;
            line-height: 1.5;
        }

        /* إطار العنوان */
        #headerFrame {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2a2a2a, stop:1 #1a1a1a);
            border: 2px solid #FFD700;
            border-radius: 15px;
            margin: 10px;
            padding: 20px;
        }

        /* المجموعات الفاخرة */
        #luxuryGroup {
            font-family: 'Segoe UI Black';
            font-size: 14px;
            font-weight: bold;
            color: #FFD700;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2a2a2a, stop:1 #1a1a1a);
            border: 2px solid #B8860B;
            border-radius: 12px;
            margin: 5px;
            padding: 15px;
        }

        #luxuryGroup::title {
            subcontrol-origin: margin;
            left: 20px;
            padding: 5px 15px;
            background: #FFD700;
            color: #000000;
            border-radius: 8px;
            font-weight: bold;
        }

        /* الأزرار الأساسية */
        #primaryButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #FFD700, stop:1 #B8860B);
            color: #000000;
            border: 2px solid #FFD700;
            border-radius: 10px;
            padding: 12px 20px;
            font-family: 'Segoe UI Black';
            font-size: 14px;
            font-weight: bold;
            min-height: 40px;
        }

        #primaryButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #FFFF00, stop:1 #FFD700);
            transform: scale(1.05);
        }

        #primaryButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #B8860B, stop:1 #8B7355);
        }

        /* الأزرار الثانوية */
        #secondaryButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4a4a4a, stop:1 #2a2a2a);
            color: #FFD700;
            border: 2px solid #B8860B;
            border-radius: 10px;
            padding: 12px 20px;
            font-family: 'Segoe UI';
            font-size: 14px;
            font-weight: bold;
            min-height: 40px;
        }

        #secondaryButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #6a6a6a, stop:1 #4a4a4a);
            border-color: #FFD700;
        }

        /* أزرار النجاح */
        #successButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #32CD32, stop:1 #228B22);
            color: #000000;
            border: 2px solid #32CD32;
            border-radius: 10px;
            padding: 12px 20px;
            font-family: 'Segoe UI Black';
            font-size: 14px;
            font-weight: bold;
            min-height: 40px;
        }

        #successButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #90EE90, stop:1 #32CD32);
        }

        /* أزرار الخطر */
        #dangerButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #FF4500, stop:1 #DC143C);
            color: #FFFFFF;
            border: 2px solid #FF4500;
            border-radius: 10px;
            padding: 12px 20px;
            font-family: 'Segoe UI Black';
            font-size: 14px;
            font-weight: bold;
            min-height: 40px;
        }

        #dangerButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #FF6347, stop:1 #FF4500);
        }

        /* التسميات */
        #statusLabel, #connectionStatus, #showTabsStatus, #deviceInfo {
            font-family: 'Segoe UI';
            font-size: 14px;
            font-weight: bold;
            color: #FFD700;
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid #B8860B;
            border-radius: 8px;
            padding: 10px;
            margin: 5px;
        }

        /* مربعات الاختيار الفاخرة */
        #luxuryCheckbox {
            font-family: 'Segoe UI Black';
            font-size: 14px;
            font-weight: bold;
            color: #FFD700;
            spacing: 10px;
        }

        #luxuryCheckbox::indicator {
            width: 20px;
            height: 20px;
            border: 2px solid #FFD700;
            border-radius: 5px;
            background: #1a1a1a;
        }

        #luxuryCheckbox::indicator:checked {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #FFD700, stop:1 #B8860B);
            border-color: #FFFF00;
        }

        #luxuryCheckbox::indicator:hover {
            border-color: #FFFF00;
            background: rgba(255, 215, 0, 0.2);
        }

        /* شريط الحالة الفاخر */
        #luxuryStatusBar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2a2a2a, stop:1 #1a1a1a);
            color: #FFD700;
            border-top: 2px solid #B8860B;
            font-family: 'Segoe UI';
            font-size: 12px;
            font-weight: bold;
            padding: 5px;
        }

        /* تأثيرات الحركة */
        QPushButton {
            transition: all 0.3s ease;
        }

        QPushButton:disabled {
            background: #3a3a3a;
            color: #666666;
            border-color: #555555;
        }
        """

        self.setStyleSheet(style)

    def update_status(self, message, color="#FFD700"):
        """تحديث شريط الحالة"""
        self.status_bar.showMessage(message)

    def check_adb_status(self):
        """فحص حالة ADB"""
        def check_thread():
            if os.path.exists(self.adb_path):
                self.adb_status.setText("✅ ADB موجود وجاهز")
                self.adb_status.setStyleSheet("color: #32CD32;")
                self.update_status("🚀 ADB جاهز - يمكنك الاتصال بـ GameLoop")
                self.connect_btn.setEnabled(True)
            else:
                self.download_adb()

        thread = threading.Thread(target=check_thread, daemon=True)
        thread.start()

    def download_adb(self):
        """تحميل ADB"""
        try:
            self.adb_status.setText("📥 جاري تحميل ADB...")
            self.adb_status.setStyleSheet("color: #FFA500;")
            self.update_status("📥 جاري تحميل ADB platform-tools...")

            adb_url = "https://dl.google.com/android/repository/platform-tools-latest-windows.zip"
            zip_file = "platform-tools.zip"

            urllib.request.urlretrieve(adb_url, zip_file)

            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall('.')

            os.remove(zip_file)

            if os.path.exists(self.adb_path):
                self.adb_status.setText("✅ تم تحميل ADB بنجاح")
                self.adb_status.setStyleSheet("color: #32CD32;")
                self.update_status("🚀 ADB جاهز - يمكنك الاتصال بـ GameLoop")
                self.connect_btn.setEnabled(True)
            else:
                raise Exception("فشل في استخراج ADB")

        except Exception as e:
            self.adb_status.setText(f"❌ فشل في تحميل ADB: {str(e)}")
            self.adb_status.setStyleSheet("color: #FF4500;")
            self.update_status(f"❌ خطأ في تحميل ADB: {str(e)}")

    def run_adb_command(self, command):
        """تشغيل أمر ADB"""
        try:
            full_command = f'"{self.adb_path}" {command}'
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=10)
            return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
        except subprocess.TimeoutExpired:
            return False, "", "انتهت مهلة الأمر"
        except Exception as e:
            return False, "", str(e)

    def connect_to_gameloop(self):
        """الاتصال بـ GameLoop"""
        def connect_thread():
            self.update_status("🔍 جاري البحث عن GameLoop...")

            try:
                # البحث عن أجهزة متصلة
                success, output, error = self.run_adb_command("devices")

                if not success:
                    self.update_status("❌ فشل في تشغيل ADB")
                    return

                # تحليل قائمة الأجهزة
                devices = []
                lines = output.split('\n')[1:]
                for line in lines:
                    if line.strip() and '\t' in line:
                        device_id, status = line.split('\t')
                        if status == 'device':
                            devices.append(device_id)

                if not devices:
                    # محاولة الاتصال بـ GameLoop على المنافذ الشائعة
                    gameloop_ports = ['21503', '21513', '21523', '21533']

                    for port in gameloop_ports:
                        self.update_status(f"🔄 محاولة الاتصال بالمنفذ {port}...")
                        success, _, _ = self.run_adb_command(f"connect 127.0.0.1:{port}")

                        if success:
                            time.sleep(2)
                            success, output, _ = self.run_adb_command("devices")
                            if success and '127.0.0.1:' + port in output:
                                devices.append(f"127.0.0.1:{port}")
                                break

                if devices:
                    device_id = devices[0]
                    self.current_device = device_id

                    # الحصول على معلومات الجهاز
                    success, model, _ = self.run_adb_command(f"-s {device_id} shell getprop ro.product.model")
                    success2, android_version, _ = self.run_adb_command(f"-s {device_id} shell getprop ro.build.version.release")

                    device_info = f"الجهاز: {model if success else 'غير معروف'}"
                    if success2:
                        device_info += f" | Android {android_version}"

                    self.connection_status.setText(f"✅ متصل: {device_id}")
                    self.connection_status.setStyleSheet("color: #32CD32;")
                    self.device_info.setText(device_info)

                    # تفعيل أزرار التحكم
                    self.enable_tabs_btn.setEnabled(True)
                    self.disable_tabs_btn.setEnabled(True)

                    self.update_status("✅ تم الاتصال بنجاح - جاري فحص حالة Show Tabs...")
                    self.check_show_tabs_status()

                else:
                    self.connection_status.setText("❌ لم يتم العثور على GameLoop")
                    self.connection_status.setStyleSheet("color: #FF4500;")
                    self.device_info.setText("تأكد من تشغيل GameLoop وتفعيل USB Debugging")
                    self.update_status("❌ لم يتم العثور على GameLoop")

                    QMessageBox.warning(self, "لم يتم العثور على GameLoop",
                                      "تأكد من:\n"
                                      "1. تشغيل GameLoop\n"
                                      "2. تفعيل Developer Options\n"
                                      "3. تفعيل USB Debugging\n"
                                      "4. السماح لـ ADB بالاتصال")

            except Exception as e:
                self.update_status(f"❌ خطأ في الاتصال: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"فشل في الاتصال:\n{str(e)}")

        thread = threading.Thread(target=connect_thread, daemon=True)
        thread.start()

    def check_show_tabs_status(self):
        """فحص حالة Show Tabs"""
        if not self.current_device:
            return

        try:
            success, output, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['get_show_taps_status']}")

            if success:
                show_tabs_enabled = output.strip() == '1'
                status_text = "مفعل ✅" if show_tabs_enabled else "غير مفعل ❌"
                color = "#32CD32" if show_tabs_enabled else "#FF4500"

                self.show_tabs_status.setText(f"📋 Show Tabs: {status_text}")
                self.show_tabs_status.setStyleSheet(f"color: {color};")

            else:
                self.show_tabs_status.setText("📋 Show Tabs: خطأ في القراءة")
                self.show_tabs_status.setStyleSheet("color: #FFA500;")

        except Exception as e:
            print(f"خطأ في فحص حالة Show Tabs: {e}")

    def enable_show_tabs(self):
        """تفعيل Show Tabs"""
        def enable_thread():
            self.update_status("🔄 جاري تفعيل Show Tabs...")

            try:
                success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['enable_show_taps']}")

                if success:
                    self.update_status("✅ تم تفعيل Show Tabs بنجاح!")
                    self.check_show_tabs_status()

                    QMessageBox.information(self, "نجح التفعيل",
                                          "✅ تم تفعيل Show Tabs بنجاح!\n"
                                          "الآن ستظهر النقاط عند اللمس في GameLoop")
                else:
                    self.update_status(f"❌ فشل في تفعيل Show Tabs: {error}")
                    QMessageBox.critical(self, "خطأ", f"فشل في تفعيل Show Tabs:\n{error}")

            except Exception as e:
                self.update_status(f"❌ خطأ: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ:\n{str(e)}")

        thread = threading.Thread(target=enable_thread, daemon=True)
        thread.start()

    def disable_show_tabs(self):
        """إلغاء تفعيل Show Tabs"""
        def disable_thread():
            self.update_status("🔄 جاري إلغاء تفعيل Show Tabs...")

            try:
                success, _, error = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['disable_show_taps']}")

                if success:
                    self.update_status("✅ تم إلغاء تفعيل Show Tabs بنجاح!")
                    self.check_show_tabs_status()

                    QMessageBox.information(self, "تم الإلغاء",
                                          "❌ تم إلغاء تفعيل Show Tabs بنجاح!\n"
                                          "لن تظهر النقاط عند اللمس في GameLoop")
                else:
                    self.update_status(f"❌ فشل في إلغاء التفعيل: {error}")
                    QMessageBox.critical(self, "خطأ", f"فشل في إلغاء التفعيل:\n{error}")

            except Exception as e:
                self.update_status(f"❌ خطأ: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ:\n{str(e)}")

        thread = threading.Thread(target=disable_thread, daemon=True)
        thread.start()

    def toggle_hide_dots(self, state):
        """تبديل حالة إخفاء النقاط"""
        if not self.current_device:
            QMessageBox.warning(self, "تحذير", "يرجى الاتصال بـ GameLoop أولاً")
            self.hide_dots_check.setChecked(False)
            return

        # التحقق من أن Show Tabs مفعل
        success, output, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['get_show_taps_status']}")
        if not success or output.strip() != '1':
            QMessageBox.warning(self, "تحذير", "يجب تفعيل Show Tabs أولاً لاستخدام هذا الخيار")
            self.hide_dots_check.setChecked(False)
            return

        def toggle_thread():
            if state == Qt.Checked:
                # إخفاء النقاط البيضاء
                methods = [
                    ('🎯 الطريقة الخاصة بـ GameLoop', 'gameloop_ultimate_hide'),
                    ('🔄 طريقة التبديل السريع', 'hide_method_1'),
                    ('🔒 طريقة الإعدادات الآمنة', 'hide_method_2'),
                    ('🌐 طريقة الإعدادات العامة', 'hide_method_3'),
                    ('⚙️ طريقة خصائص النظام', 'hide_method_4'),
                    ('⏱️ طريقة التأخير', 'hide_method_5'),
                    ('🗑️ طريقة حذف الإعدادات', 'hide_method_6'),
                    ('❌ طريقة القيمة الفارغة', 'hide_method_7'),
                    ('🔘 طريقة الشفافية', 'hide_method_8'),
                    ('🔸 طريقة التصغير الشديد', 'hide_method_9'),
                    ('📏 طريقة تقليل الحجم', 'hide_method_10')
                ]

                success = False
                successful_method = ""

                for method_name, method_key in methods:
                    self.update_status(f"🔄 جاري تجربة {method_name}...")
                    QApplication.processEvents()

                    success, _, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands[method_key]}")

                    if success:
                        successful_method = method_name
                        self.update_status(f"✅ نجح! تم إخفاء النقاط البيضاء باستخدام {method_name}!")
                        break
                    else:
                        time.sleep(0.3)

                if success:
                    QMessageBox.information(self, "🎉 نجح الإخفاء!",
                                          f"✅ تم إخفاء النقاط البيضاء بنجاح!\n\n"
                                          f"الطريقة المستخدمة: {successful_method}\n\n"
                                          f"الآن النقاط البيضاء مخفية في GameLoop! 🔘")
                else:
                    self.update_status("⚠️ فشل في إخفاء النقاط بجميع الطرق")
                    self.hide_dots_check.setChecked(False)
                    QMessageBox.warning(self, "فشل الإخفاء",
                                      "❌ فشل في إخفاء النقاط بجميع الطرق\n\n"
                                      "جرب:\n"
                                      "1. إعادة تشغيل GameLoop\n"
                                      "2. إعادة تفعيل Show Tabs\n"
                                      "3. المحاولة مرة أخرى")
            else:
                # إعادة إظهار النقاط
                self.update_status("🔄 جاري إعادة إظهار النقاط...")
                success, _, _ = self.run_adb_command(f"-s {self.current_device} shell {self.adb_commands['restore_touches']}")

                if success:
                    self.update_status("✅ تم إعادة إظهار النقاط بنجاح!")
                    QMessageBox.information(self, "تم الإظهار", "✅ تم إعادة إظهار النقاط البيضاء! ⚪")
                else:
                    self.update_status("❌ فشل في إعادة إظهار النقاط")
                    self.hide_dots_check.setChecked(True)

        thread = threading.Thread(target=toggle_thread, daemon=True)
        thread.start()


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # استخدام نمط Fusion للحصول على مظهر أفضل

    # إعداد الخط العام
    font = QFont("Segoe UI", 10)
    app.setFont(font)

    # إنشاء النافذة الرئيسية
    window = GameLoopADBPro()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
