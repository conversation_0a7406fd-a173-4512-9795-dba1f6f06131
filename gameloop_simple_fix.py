#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GameLoop Simple Fix - حل بسيط ومباشر لإخفاء النقاط البيضاء
"""

import os
import subprocess
import tkinter as tk
from tkinter import messagebox
import threading
import time

class GameLoopSimpleFix:
    def __init__(self):
        self.root = tk.Tk()
        self.adb_path = "platform-tools/adb.exe"
        self.current_device = None
        
        # الحل البسيط - طريقة واحدة مجربة
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        self.root.title("🎮 GameLoop Simple Fix - حل بسيط")
        self.root.geometry("600x400")
        self.root.configure(bg='#2b2b2b')
        
    def create_widgets(self):
        # العنوان
        title = tk.Label(self.root, 
                        text="🎮 GameLoop Simple Fix",
                        font=('Arial', 20, 'bold'),
                        bg='#2b2b2b', fg='#00ff00')
        title.pack(pady=20)
        
        # الوصف
        desc = tk.Label(self.root,
                       text="حل بسيط ومباشر لإخفاء النقاط البيضاء في GameLoop",
                       font=('Arial', 12),
                       bg='#2b2b2b', fg='#ffffff')
        desc.pack(pady=10)
        
        # زر الاتصال
        self.connect_btn = tk.Button(self.root,
                                    text="🔌 اتصال بـ GameLoop",
                                    font=('Arial', 14, 'bold'),
                                    bg='#4CAF50', fg='white',
                                    command=self.connect_gameloop,
                                    width=20, height=2)
        self.connect_btn.pack(pady=10)
        
        # زر تفعيل Show Tabs
        self.enable_btn = tk.Button(self.root,
                                   text="✅ تفعيل Show Tabs",
                                   font=('Arial', 14, 'bold'),
                                   bg='#2196F3', fg='white',
                                   command=self.enable_show_tabs,
                                   width=20, height=2,
                                   state='disabled')
        self.enable_btn.pack(pady=10)
        
        # زر الحل البسيط
        self.fix_btn = tk.Button(self.root,
                                text="🔥 الحل البسيط - إخفاء النقاط",
                                font=('Arial', 14, 'bold'),
                                bg='#FF5722', fg='white',
                                command=self.simple_fix,
                                width=25, height=2,
                                state='disabled')
        self.fix_btn.pack(pady=10)
        
        # شريط الحالة
        self.status = tk.Label(self.root,
                              text="🚀 جاهز - اضغط اتصال بـ GameLoop",
                              font=('Arial', 10),
                              bg='#2b2b2b', fg='#ffff00')
        self.status.pack(pady=20)
        
    def run_adb(self, command):
        """تشغيل أمر ADB"""
        try:
            full_cmd = f'"{self.adb_path}" {command}'
            result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True, timeout=10)
            return result.returncode == 0, result.stdout.strip()
        except:
            return False, ""
            
    def connect_gameloop(self):
        """اتصال بسيط بـ GameLoop"""
        def connect():
            self.status.config(text="🔍 جاري البحث عن GameLoop...")
            self.root.update()
            
            # جرب المنافذ الشائعة
            ports = ['21503', '21513', '21523', '21533']
            
            for port in ports:
                self.status.config(text=f"🔄 جاري الاتصال بالمنفذ {port}...")
                self.root.update()
                
                success, _ = self.run_adb(f"connect 127.0.0.1:{port}")
                if success:
                    time.sleep(1)
                    success2, output = self.run_adb("devices")
                    if success2 and f"127.0.0.1:{port}" in output:
                        self.current_device = f"127.0.0.1:{port}"
                        self.status.config(text=f"✅ متصل بـ GameLoop على المنفذ {port}")
                        self.enable_btn.config(state='normal')
                        self.fix_btn.config(state='normal')
                        return
            
            self.status.config(text="❌ لم يتم العثور على GameLoop")
            messagebox.showerror("خطأ", "لم يتم العثور على GameLoop\nتأكد من تشغيله أولاً")
            
        threading.Thread(target=connect, daemon=True).start()
        
    def enable_show_tabs(self):
        """تفعيل Show Tabs"""
        def enable():
            self.status.config(text="🔄 جاري تفعيل Show Tabs...")
            self.root.update()
            
            success, _ = self.run_adb(f"-s {self.current_device} shell settings put system show_touches 1")
            
            if success:
                self.status.config(text="✅ تم تفعيل Show Tabs بنجاح!")
                messagebox.showinfo("نجح", "✅ تم تفعيل Show Tabs!\nالآن ستظهر النقاط عند اللمس")
            else:
                self.status.config(text="❌ فشل في تفعيل Show Tabs")
                messagebox.showerror("خطأ", "فشل في تفعيل Show Tabs")
                
        threading.Thread(target=enable, daemon=True).start()
        
    def simple_fix(self):
        """الحل البسيط لإخفاء النقاط"""
        def fix():
            self.status.config(text="🔥 جاري تطبيق الحل البسيط...")
            self.root.update()
            
            # الحل البسيط: تفعيل Show Tabs مع إعدادات خاصة
            commands = [
                # الطريقة الأولى: تفعيل مع إلغاء المؤشر
                f"-s {self.current_device} shell settings put system show_touches 1 && settings put system pointer_location 0",
                
                # الطريقة الثانية: تفعيل مع تصفير الحجم
                f"-s {self.current_device} shell settings put system show_touches 1 && settings put system pointer_size 0",
                
                # الطريقة الثالثة: تفعيل مع إعدادات مخفية
                f"-s {self.current_device} shell settings put system show_touches 1 && settings put global show_touches 0",
                
                # الطريقة الرابعة: إعادة تعيين كاملة
                f"-s {self.current_device} shell settings put system show_touches 0 && settings put system show_touches 1 && settings put system show_touches 0 && settings put system show_touches 1",
                
                # الطريقة الخامسة: حذف وإعادة إنشاء
                f"-s {self.current_device} shell settings delete system show_touches && settings put system show_touches 1"
            ]
            
            for i, cmd in enumerate(commands, 1):
                self.status.config(text=f"🔄 جاري تجربة الطريقة {i}/5...")
                self.root.update()
                
                success, _ = self.run_adb(cmd)
                
                if success:
                    self.status.config(text=f"✅ نجح الحل! تم استخدام الطريقة {i}")
                    messagebox.showinfo("🎉 نجح الحل!", 
                                      f"✅ تم إخفاء النقاط البيضاء بنجاح!\n\n"
                                      f"تم استخدام الطريقة رقم {i}\n\n"
                                      f"الآن النقاط مخفية في GameLoop! 🎮")
                    return
                    
                time.sleep(0.5)
            
            # إذا فشلت كل الطرق
            self.status.config(text="⚠️ لم تنجح أي طريقة - جرب إعادة تشغيل GameLoop")
            messagebox.showwarning("لم ينجح الحل", 
                                 "⚠️ لم تنجح أي من الطرق\n\n"
                                 "جرب:\n"
                                 "1. إعادة تشغيل GameLoop\n"
                                 "2. تفعيل Developer Options\n"
                                 "3. المحاولة مرة أخرى")
                
        threading.Thread(target=fix, daemon=True).start()
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = GameLoopSimpleFix()
    app.run()
